// src/composables/usePermissions.ts
import { computed } from 'vue'
import { useAuthStore } from '@/stores/authStore' // Asumiendo que tienes uno

export function usePermissions() {
  const auth = useAuthStore()

  const role = computed(() => auth.user?.role || '')

  const canSeeComparador = computed(() => role.value === 'admin' || role.value === 'sourcing')
  const canViewLogs = computed(() => role.value === 'admin')
  const canCreateProveedor = computed(() => role.value === 'admin')

  return {
    role,
    canSeeComparador,
    canViewLogs,
    canCreateProveedor,
  }
}
