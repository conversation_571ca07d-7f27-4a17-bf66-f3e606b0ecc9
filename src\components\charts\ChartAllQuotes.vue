<template>
  <div>
    <div class="mb-3">
      <label class="font-bold mr-2">Selecciona una fecha: </label>
      <Calendar v-model="selectedDate" dateFormat="dd/mm/yy" showIcon />
    </div>

    <!-- <PERSON><PERSON> (Semana / Mes / Año) -->
    <div class="mb-4">
      <label class="font-bold mr-2">Agrupar por: </label>
      <SelectButton
        v-model="selectedFilter"
        :options="['Semana', 'Mes', 'Año']" />
    </div>

    <Chart type="bar" :data="chartData" :options="chartOptions" />
  </div>
</template>

<script setup lang="ts">
import Chart from "primevue/chart";
import { ref, computed } from "vue";
import Calendar from "primevue/calendar";
import SelectButton from "primevue/selectbutton";
import {
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
} from "date-fns";

interface DataEntry {
  date: string;
  createdquotes: number;
  requestedquotes: number;
  editedquotes: number;
}

const allData: DataEntry[] = [
  {
    date: "2025-01-01",
    createdquotes: 1,
    requestedquotes: 2,
    editedquotes: 1,
  },
  {
    date: "2025-01-05",
    createdquotes: 1,
    requestedquotes: 5,
    editedquotes: 1,
  },
  {
    date: "2025-01-10",
    createdquotes: 2,
    requestedquotes: 1,
    editedquotes: 3,
  },
  {
    date: "2025-01-15",
    createdquotes: 5,
    requestedquotes: 0,
    editedquotes: 0,
  },
  {
    date: "2025-01-20",
    createdquotes: 0,
    requestedquotes: 0,
    editedquotes: 0,
  },
  {
    date: "2025-01-25",
    createdquotes: 1,
    requestedquotes: 1,
    editedquotes: 2,
  },
  {
    date: "2025-02-01",
    createdquotes: 2,
    requestedquotes: 5,
    editedquotes: 1,
  },
  {
    date: "2025-02-05",
    createdquotes: 1,
    requestedquotes: 3,
    editedquotes: 1,
  },
  {
    date: "2025-02-10",
    createdquotes: 1,
    requestedquotes: 1,
    editedquotes: 5,
  },
  {
    date: "2025-02-15",
    createdquotes: 3,
    requestedquotes: 1,
    editedquotes: 3,
  },
  {
    date: "2025-02-20",
    createdquotes: 3,
    requestedquotes: 1,
    editedquotes: 1,
  },
  {
    date: "2025-02-25",
    createdquotes: 6,
    requestedquotes: 1,
    editedquotes: 6,
  },
];

// Tipado de las variables reactivas
const selectedDate = ref<Date>(new Date());
const selectedFilter = ref<"Semana" | "Mes" | "Año">("Año");

// Computed para filtrar los datos según el período seleccionado
const filteredData = computed<DataEntry[]>(() => {
  const selected = selectedDate.value;
  let startDate: Date, endDate: Date;

  switch (selectedFilter.value) {
    case "Semana":
      startDate = startOfWeek(selected, { weekStartsOn: 1 });
      endDate = endOfWeek(selected, { weekStartsOn: 1 });
      break;
    case "Mes":
      startDate = startOfMonth(selected);
      endDate = endOfMonth(selected);
      break;
    case "Año":
      startDate = startOfYear(selected);
      endDate = endOfYear(selected);
      break;
  }

  return allData.filter((d) => {
    const dataDate = new Date(d.date);
    return dataDate >= startDate! && dataDate <= endDate!;
  });
});

// Computed para generar los datos del gráfico
const chartData = computed(() => {
  const labels = filteredData.value.map((d) => d.date);
  const createdQuotes = filteredData.value.map((d) => d.createdquotes);
  const requestedQuotes = filteredData.value.map((d) => d.requestedquotes);
  const editedQuotes = filteredData.value.map((d) => d.editedquotes);

  return {
    labels,
    datasets: [
      {
        label: "Created Quotes",
        backgroundColor: "#42A5F5",
        data: createdQuotes,
      },
      {
        label: "Resquested Quotes",
        backgroundColor: "#FFA726",
        data: requestedQuotes,
      },
      {
        label: "Edited Quotes",
        backgroundColor: "#A020F0",
        data: editedQuotes,
      },
    ],
  };
});

// Opciones del gráfico tipadas
const chartOptions = ref<Record<string, unknown>>({
  responsive: true,
  plugins: { legend: { position: "top" } },
});
</script>
