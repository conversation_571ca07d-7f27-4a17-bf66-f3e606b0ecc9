// src/mocks/mockItems.ts
import type { Item } from '@/interfaces/ItemsInterface'

export const mockItems: Item[] = [
  {
    material: 3206449000336,
    cantidadPedido: 1500,
    precioNetoPedido: 12.75,
    level1: "Hogar",
    level2: "<PERSON>cin<PERSON>",
    level3: "Utensilios",
    level4: "Espátulas de silicona",
    temporada: "2025-Otoño",
    textoTemporada: "Otoño 2025",
    grupoAnalisis: "Grupo C",
    proveedorCod: "PROV00111",
    proveedorDescripcion: "Utensilios Pro Ltda.",
    fob: 13.2,
    sku: 3206449000336,
    madreId: "MAD-001",
    feriaId: "FERIA-2025-OT",
    madreDescripcion: "Set de utensilios de cocina de silicona",
  },
  {
    material: 3118757000130,
    cantidadPedido: 2000,
    precioNetoPedido: 9.5,
    level1: "Textil",
    level2: "Dormitorio",
    level3: "Ropa de cama",
    level4: "Sábanas 2 plazas",
    temporada: "2025-Primavera",
    textoTemporada: "Primavera 2025",
    grupoAnalisis: "Grupo B",
    proveedorCod: "PROV00222",
    proveedorDescripcion: "Textiles del Sur",
    fob: 10.1,
    sku: 3118757000130,
    madreId: "MAD-002",
    feriaId: "FERIA-2025-PR",
    madreDescripcion: "Juego de sábanas 2 plazas algodón 300 hilos",
  },
  {
    material: 3090042003494,
    cantidadPedido: 800,
    precioNetoPedido: 35.0,
    level1: "Decoración",
    level2: "Iluminación",
    level3: "Lámparas de mesa",
    level4: "Lámparas de cerámica",
    temporada: "2025-Invierno",
    textoTemporada: "Invierno 2025",
    grupoAnalisis: "Grupo A",
    proveedorCod: "PROV00333",
    proveedorDescripcion: "Luz & Diseño",
    fob: 36.5,
    sku: 3090042003494,
    madreId: "MAD-003",
    feriaId: "FERIA-2025-IN",
    madreDescripcion: "Lámpara de cerámica con pantalla de lino",
  },
];
