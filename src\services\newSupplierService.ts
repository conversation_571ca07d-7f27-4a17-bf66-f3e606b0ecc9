import axios from "axios";

const API_URL = 'http://localhost:8090/api/suppliers'; // Reemplaza por nuevo endpoint

export const createSupplier = (jsonData: object) => {
    console.log("Sending data to backend:", jsonData);
    try {
      console.log("Sending data to backend:", jsonData);
      return axios.post(API_URL, jsonData, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      console.error("Error sending data to backend:", error);
    }
  
  };

export const SupplierService = {
  async getSupplierByCompantyName(companyName: string){
    try {
      const response = await axios.get(`${API_URL}/company-name/${companyName}`);
      console.log('Supplier data received:', response.data);
      return response.data;
      
    } catch (error) {
      console.error('Error fetching supplier:', error);
      throw error;
    }
  },

  // You can add other supplier-related methods here later
};