import axios from "axios";

const API_URL_SUPPLIER_MANAGMENT = import.meta.env.VITE_API_URL_SUPPLIER_MANAGMENT;
const API_URL = `${API_URL_SUPPLIER_MANAGMENT}/api/suppliers`

export const createSupplier = (jsonData: object) => {
    console.log("Sending data to backend:", jsonData);
    try {
      console.log("Sending data to backend:", jsonData);
      return axios.post(API_URL, jsonData, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      console.error("Error sending data to backend:", error);
    }

  };

export const SupplierService = {
  async getSupplierById(id: number) {
    try {
      const response = await axios.get(`${API_URL}/id/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching supplier by ID:', error);
      throw error;
    }
  },

  async getSupplierByCompanyName(companyName: string) {
    try {
      const response = await axios.get(`${API_URL}/company-name/${companyName}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching supplier by company name:', error);
      throw error;
    }
  },

  async getAllSuppliers() {
    try {
      const response = await axios.get(API_URL);
      console.log('All suppliers data received:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching all suppliers:', error);
      throw error;
    }
  }
};