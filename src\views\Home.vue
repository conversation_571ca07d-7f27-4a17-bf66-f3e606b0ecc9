<template>
  <div class="container-fluid min-vh-100 py-4">
    <UserHeader
      :title="`Hi, ${userName}`"
      icon="person-raised-hand"
      @logout="logout"
    />

    <main class="flex-grow-1 d-flex justify-content-center align-items-center">
      <div class="container">
        <div class="row justify-content-center mt-2">
          <h1
            class="text-center main-title animate-fade-down animate-once animate-duration-500"
          >
            B2B Sourcing Platform
          </h1>

          <!-- Cards principales con submenús -->
          <div class="cards-container">
            <!-- Fila 1: Cotización y Proveedores -->
            <div class="cards-row">
              <!-- Card de Cotización 2-->
              <HomeCardWithSubmenu
                v-if="hasRole('Diseño') || hasRole('Sourcing') || hasRole('Auditor')"
                icon="bi bi-receipt-cutoff"
                title="Cotización"
                subtitle="Gestión de cotizaciones"
                type="cotizacion"
                :menuItems="[
                  {
                    title: 'Crear Cotización',
                    route: '/cotizaciones/nueva',
                    icon: 'bi bi-plus-circle',
                  },
                  {
                    title: 'Listar Cotizaciones',
                    route: '/cotizaciones/view',
                    icon: 'bi bi-list-ul',
                  },
                  {
                    title: 'Comparar Cotizaciones',
                    route: '/cotizaciones/comparador',
                    icon: 'bi bi-bar-chart',
                  },
                ]"
                class="animate-fade-up animate-once animate-duration-500 animate-delay-[200ms]"
              />

              <!-- Card de Proveedores -->
              <HomeCardWithSubmenu
                v-if="hasRole('Sourcing') || hasRole('Auditor')"
                
                icon="bi bi-people"
                title="Proveedores"
                subtitle="Gestión de proveedores"
                type="proveedores"
                :menuItems="[
                  {
                    title: 'Crear Proveedor',
                    route: '/nuevoProveedor/mail',
                    icon: 'bi bi-person-plus',
                  },
                  {
                    title: 'Listar Proveedores',
                    route: '/proveedores/listaProveedores',
                    icon: 'bi bi-list-check',
                  },
                  {
                    title: 'Gestionar Proveedores',
                    route: '/proveedores/gestion',
                    icon: 'bi bi-gear',
                  },
                ]"
                class="animate-fade-up animate-once animate-duration-500 animate-delay-[400ms]"
              />
            </div>

            <!-- Fila 2: Auditoría y Reportes -->
            <div class="cards-row">
              <!-- Card de Auditoría -->
              <HomeCardWithSubmenu
                v-if="hasRole('Auditor')"
                icon="bi bi-clipboard-check"
                title="Auditoría"
                subtitle="Gestión de auditorías"
                type="auditoria"
                :menuItems="[
                  {
                    title: 'Listar Fábricas',
                    route: '/auditoria/fabricas',
                    icon: 'bi bi-building',
                  },
                  {
                    title: 'Gestionar Fábricas',
                    route: '/auditoria/gestion',
                    icon: 'bi bi-tools',
                  },
                ]"
                class="animate-fade-up animate-once animate-duration-500 animate-delay-[600ms]"
              />

              <!-- Card de Reportes -->
              <HomeCardWithSubmenu
                v-if="hasRole('Sourcing') || hasRole('Auditor')"
                icon="bi bi-graph-up"
                title="Reportes"
                subtitle="Visualización de datos"
                type="reportes"
                :menuItems="[
                  {
                    title: 'Proveedores',
                    route: '/report?view=proveedores',
                    icon: 'bi bi-people-fill',
                  },
                  {
                    title: 'Cotizaciones',
                    route: '/report?view=cotizaciones',
                    icon: 'bi bi-file-earmark-text-fill',
                  },
                  {
                    title: 'Usuario',
                    route: '/report?view=usuario',
                    icon: 'bi bi-person-fill',
                  },
                ]"
                class="animate-fade-up animate-once animate-duration-500 animate-delay-[800ms]"
              />
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useHome } from "@/composables/useHome";
import HomeCardWithSubmenu from "@/components/cards/HomeCardWithSubmenu.vue";
import UserHeader from "@/components/layout/headers/header.vue";
import keycloak from "@/auth/keycloak";
import { ref } from "vue";
import { useAuth } from "@/composables/useAuth";
import { jwtDecode } from "jwt-decode";

const { hasRole } = useAuth();
const token = keycloak.token;
if (token) {
  const decoded: any = jwtDecode(token);
  console.log("🧾 Token Decodificado:", decoded);
  console.log("Has roles:", decoded.resource_access?.myclient?.roles);

}

const userName = ref("");

if (keycloak.authenticated) {
  const token = keycloak.tokenParsed;

  userName.value = token?.name || token?.preferred_username || "User";
}
const { logout } = useHome();



</script>

<style scoped>
.container-fluid {
  padding: 0;
}

.main-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #7b7b7b;
  margin-bottom: 2rem;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.388);
}

/* Estilos para las estadísticas */

.cotizacion-stats {
  background: linear-gradient(135deg, #4158d0, #c850c0);
}

.proveedores-stats {
  background: linear-gradient(135deg, #0093e9, #80d0c7);
}

.auditoria-stats {
  background: linear-gradient(135deg, #ff9a8b, #ff6a88);
}

.reportes-stats {
  background: linear-gradient(135deg, #ffb347, #ffcc33);
}

/* Estilos para la disposición de tarjetas */
.cards-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2.5rem;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.cards-row {
  display: flex;
  justify-content: center;
  gap: 2.5rem;
  width: 100%;
  max-width: 650px;
}

@media (max-width: 768px) {
  .cards-row {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }

  .cards-container {
    gap: 2rem;
  }
}

</style>
