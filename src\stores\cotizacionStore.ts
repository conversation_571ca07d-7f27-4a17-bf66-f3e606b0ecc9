// src/stores/cotizacionStore.ts
import { defineStore } from "pinia";
import { ref, computed } from "vue";
import type { Cotizacion } from "@/interfaces/CotizacionInterface";
import type { Item } from "@/interfaces/ItemsInterface";
import type {
  Proveedor,
  PersonalizacionCorreo,
} from "@/interfaces/ProveedorInterface";
import { enviarCotizacion } from "@/services/cotizacionService";

export const useCotizacionStore = defineStore("cotizacion", () => {
  // Cotización actual en edición
  const nombre = ref("");
  const proveedores = ref<Proveedor[]>([]);
  const items = ref<Item[]>([]);
  const filtrosSeleccionados = ref<Record<string, string>>({});
  const skusManuales = ref<Item[]>([]);

  // Lista de cotizaciones en proceso
  const cotizacionesEnProceso = ref<Cotizacion[]>([]);

  // Lista de cotizaciones seleccionadas para enviar
  const cotizacionesSeleccionadas = ref<Cotizacion[]>([]);

  function setSkusManuales(nuevos: Item[]) {
    skusManuales.value = nuevos;
  }

  // Agregar proveedor (sin duplicados por email o nombre)
  function agregarProveedor(prov: Proveedor) {
      proveedores.value.push(prov);
    // console.log('Proveedores recibidos', proveedores.value)
  }

  // Eliminar proveedor por índice
  function eliminarProveedor(index: number) {
    proveedores.value.splice(index, 1);
  }

  // Agregar item a la cotización
  function agregarItem(item: Item) {
    // Verificar si el item ya existe para evitar duplicados
    const itemExistente = items.value.find((i) => i.sku === item.sku);
    if (!itemExistente) {
      items.value.push(item);
    }
  }

  // Eliminar item por índice
  function eliminarItem(index: number) {
    items.value.splice(index, 1);
  }

  // Actualizar correo personalizado de un proveedor
  function setCorreoPersonalizado(
    email: string,
    correo: PersonalizacionCorreo
  ) {
    const proveedor = proveedores.value.find((p) => p.email === email);
    if (proveedor) {
      proveedor.correoPersonalizado = correo;
    }
  }

  // Guardar la cotización actual en la lista de cotizaciones en proceso
  function guardarCotizacionEnProceso() {
    if (nombre.value.trim() === "") {
      throw new Error("La cotización debe tener un nombre");
    }

    if (items.value.length === 0) {
      throw new Error("La cotización debe tener al menos un material");
    }

    const nuevaCotizacion: Cotizacion = {
      id: `temp-${Date.now()}`, // ID temporal
      nombre: nombre.value,
      fechaCreacion: new Date().toISOString(),
      tipo: "enviada",
      proveedores: [...proveedores.value],
      items: [...items.value],
      estado: "pendiente",
      modificada: false,
    };

    cotizacionesEnProceso.value.push(nuevaCotizacion);

    // Limpiar la cotización actual
    reset();

    return nuevaCotizacion;
  }

  // Eliminar una cotización en proceso
  function eliminarCotizacionEnProceso(index: number) {
    cotizacionesEnProceso.value.splice(index, 1);
  }

  // Seleccionar una cotización para enviar
  function seleccionarCotizacion(cotizacion: Cotizacion) {
    const yaSeleccionada = cotizacionesSeleccionadas.value.some(
      (c) => c.id === cotizacion.id
    );
    if (!yaSeleccionada) {
      cotizacionesSeleccionadas.value.push(cotizacion);
    }
  }

  // Deseleccionar una cotización
  function deseleccionarCotizacion(index: number) {
    cotizacionesSeleccionadas.value.splice(index, 1);
  }

  // Enviar cotizaciones seleccionadas al backend
  async function enviarCotizacionesSeleccionadas() {
    for (const cotizacion of cotizacionesSeleccionadas.value) {
      console.log(
        "Enviando cotización al backend...",
        cotizacionesSeleccionadas.value
      );
      try {
        await enviarCotizacion(cotizacion);
        console.log(`Cotización ${cotizacion.nombre} enviada con éxito`);

        // Eliminar la cotización de la lista de cotizaciones en proceso
        const index = cotizacionesEnProceso.value.findIndex(
          (c) => c.id === cotizacion.id
        );
        if (index !== -1) {
          cotizacionesEnProceso.value.splice(index, 1);
        }
      } catch (error) {
        console.error(
          `Error al enviar la cotización ${cotizacion.nombre}:`,
          error
        );
      }
    }

    // Limpiar la lista de cotizaciones seleccionadas
    cotizacionesSeleccionadas.value = [];
  }

  // Enviar la cotización actual al backend (método legacy)
  async function enviar() {
    const payload: Cotizacion = {
      id: "",
      nombre: nombre.value,
      fechaCreacion: new Date().toISOString(),
      tipo: "enviada",
      proveedores: proveedores.value,
      items: items.value,
      estado: "pendiente",
      modificada: false,
    };

    console.log("Enviando cotización al backend (método legacy):", payload);
    await enviarCotizacion(payload);
  }

  // Obtener la cotización actual como objeto
  const cotizacionActual = computed<Cotizacion>(() => ({
    id: "",
    nombre: nombre.value,
    fechaCreacion: new Date().toISOString(),
    tipo: "enviada",
    proveedores: proveedores.value,
    items: items.value,
    estado: "pendiente",
    modificada: false,
  }));

  // Limpiar estado de la cotización actual
  function reset() {
    nombre.value = "";
    proveedores.value = [];
    items.value = [];
  }

  return {
    // Estado de la cotización actual
    nombre,
    proveedores,
    items,
    filtrosSeleccionados,
    skusManuales,

    // Métodos para la cotización actual
    agregarProveedor,
    eliminarProveedor,
    agregarItem,
    eliminarItem,
    setCorreoPersonalizado,
    setSkusManuales,
    reset,
    cotizacionActual,

    // Gestión de cotizaciones en proceso
    cotizacionesEnProceso,
    guardarCotizacionEnProceso,
    eliminarCotizacionEnProceso,

    // Gestión de cotizaciones seleccionadas
    cotizacionesSeleccionadas,
    seleccionarCotizacion,
    deseleccionarCotizacion,
    enviarCotizacionesSeleccionadas,

    // Método legacy
    enviar,
  };
});
