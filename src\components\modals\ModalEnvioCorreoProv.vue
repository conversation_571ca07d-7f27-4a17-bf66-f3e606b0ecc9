<template>
  <div v-if="isVisible" class="modal-overlay">
    <div class="modal-container">
      <div class="modal-header">
        <h2>Enviar Cotización por Correo</h2>
        <button class="close-button" @click="closeModal">
          <i class="pi pi-times"></i>
        </button>
      </div>
      <div class="modal-content">
        <EnvioCorreos
          :email="proveedorEmail"
          @email-sent="handleEmailSent"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import EnvioCorreos from '@/components/mail/EnvioCorreos.vue';
import type { Cotizacion } from '@/interfaces/CotizacionInterface';

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false
  },
  cotizacion: {
    type: Object as () => Cotizacion | null,
    default: null
  }
});

const emit = defineEmits(['close', 'email-sent']);

// Email del proveedor (se obtiene de la cotización)
const proveedorEmail = ref('');

// Observar cambios en la cotización para actualizar el email del proveedor
watch(() => props.cotizacion, (newCotizacion) => {
  if (newCotizacion && newCotizacion.proveedores && newCotizacion.proveedores.length > 0) {
    // Obtener el email del primer proveedor (se podría mejorar para manejar múltiples proveedores)
    proveedorEmail.value = newCotizacion.proveedores[0].email || '';
  } else {
    proveedorEmail.value = '';
  }
}, { immediate: true });

// Función para cerrar el modal
const closeModal = () => {
  emit('close');
};

// Función para manejar el envío exitoso del correo
const handleEmailSent = () => {
  emit('email-sent');
  closeModal();
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: #202124;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #5f6368;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: #e8eaed;
}

.modal-content {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

@media (max-width: 768px) {
  .modal-container {
    width: 95%;
    max-height: 95vh;
  }
}
</style>