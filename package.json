{"name": "b2b_sourcing", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --open", "build": "vue-tsc -b && vite build", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@primeuix/themes": "^1.0.0", "@primevue/themes": "^4.3.3", "@tailwindcss/vite": "^4.0.15", "axios": "^1.8.4", "bootstrap": "^5.3.3", "chart.js": "^4.4.8", "date-fns": "^4.1.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "keycloak-js": "^26.1.5", "pinia": "^3.0.1", "primeicons": "^7.0.0", "primevue": "^4.3.3", "sweetalert2": "^11.17.2", "tailwindcss-animated": "^2.0.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@primevue/forms": "^4.3.3", "@types/node": "^22.13.11", "@vitejs/plugin-vue": "^5.2.1", "@vue/compiler-sfc": "^3.5.13", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "happy-dom": "^17.4.4", "jsdom": "^26.0.0", "postcss": "^8.5.3", "sass": "^1.86.0", "tailwindcss": "^4.0.17", "typescript": "~5.7.2", "vite": "^6.2.0", "vitest": "^3.1.1", "vue-test-utils": "^0.5.0", "vue-tsc": "^2.2.4"}}