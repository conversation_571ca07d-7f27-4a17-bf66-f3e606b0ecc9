<template>
  <div v-if="visible" class="modal-backdrop">
    <div class="modal-content p-3 rounded shadow">
      <h5 class="mb-3">Agregar este SKU a la cotización: <strong>{{ item.material }}</strong></h5>

      <p class="fw-semibold text-secondary mb-2">Información del item:</p>
      <div class="item-info mb-3">
        <p><strong>Material:</strong> {{ item.material }}</p>
        <p><strong>Descripción:</strong> {{ item.descripcionMadre }}</p>
        <p><strong>Proveedor:</strong> {{ item.nombreProveedor }}</p>
      </div>

      <div class="d-flex justify-content-between">
        <button class="btn btn-primary" @click="agregarItem">
          Agregar a la cotización
        </button>
        <button class="btn btn-secondary" @click="$emit('close')">
          Cancelar
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Proveedor } from "@/interfaces/ProveedorInterface";
import { useCotizacionStore } from "@/stores/cotizacionStore";

const store = useCotizacionStore();

const props = defineProps<{
  visible: boolean;
  item: any;
  proveedores: Proveedor[];
}>();

const emit = defineEmits<{
  (e: "seleccionado", proveedorId: string): void;
  (e: "close"): void;
}>();

function agregarItem() {
  // Agregar el item directamente a la cotización
  store.agregarItem(props.item);

  // Emitir evento para mantener compatibilidad
  if (props.item.nombreProveedor) {
    emit("seleccionado", props.item.nombreProveedor);
  } else {
    emit("close");
  }
}
</script>

<style scoped>
.modal-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.modal-content {
  background-color: #ffffff;
  min-width: 600px;
  max-width: 400px;
}

li:hover{
  cursor: pointer;
  background-color: #f0f0f0;
}

.list-group-item {
  transition: background-color 0.2s ease;
}
.list-group-item:hover {
  cursor: pointer;
  background-color: #f0f0f0;
}
</style>
