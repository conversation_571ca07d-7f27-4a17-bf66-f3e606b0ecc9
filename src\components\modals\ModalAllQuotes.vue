<template>
  <div>
    <Dialog
      class="animate-fade-down animate-duration-500 animate-normal"
      v-model:visible="Boolean"
      :modal="false"
      :header="'Quotes History'"
      :style="{ width: '60vw', heigth: '60vw' }"
      :closable="false">
      <ChartAllQuotes
        class="animate-fade-down animate-duration-500 animate-delay-500 animate-normal" />

      <template #footer>
        <Button
          @click="$emit('closeQuotes')"
          label="Close"
          icon="pi pi-times"
          class="p-button-text" />
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import Dialog from "primevue/dialog";
import Button from "primevue/button";
import ChartAllQuotes from "../charts/ChartAllQuotes.vue";

defineProps({
  visible: Boolean,
});

const emit = defineEmits(["closeQuotes"]);
</script>
