<template>
  <div>
    <!-- Botón para mostrar la tabla -->
    <div class="mb-3">
      <h6 class="fw-semibold">¿Desea agregar un SKU nuevo?</h6>
      <button
        v-if="!mostrarTablaSkus"
        class="btn custom-hover-outline"
        @click="mostrarTablaSkus = true"
      >
        + Agregar nuevo Material
      </button>
    </div>

    <!-- Tabla completa (visible solo si mostrarTablaSkus es true) -->
    <div v-if="mostrarTablaSkus">
      <div class="d-flex justify-content-around">
        <button
          class="btn btn-sm custom-hover-outline  mb-3"
          @click="agregarFila"
        >
          Agregar Material
        </button>
        <button
          class="btn btn-sm btn-close-table mb-3"
          @click="mostrarTablaSkus = false"
        >
          Cerrar Tabla
        </button>
      </div>

      <div class="table-responsive">
        <table class="table custom-sku-table resizable-table">
          <colgroup>
            <col :style="{ width: columnWidths[0] + 'px' }" />
            <!-- Acción -->
            <col :style="{ width: columnWidths[1] + 'px' }" />
            <!-- Imagen -->
            <col :style="{ width: columnWidths[2] + 'px' }" />
            <!-- Feria -->
            <col :style="{ width: columnWidths[3] + 'px' }" />
            <!-- Madre -->
            <col :style="{ width: columnWidths[4] + 'px' }" />
            <!-- Descripción Madre -->
            <col :style="{ width: columnWidths[5] + 'px' }" />
            <!-- Nivel 1 -->
            <col :style="{ width: columnWidths[6] + 'px' }" />
            <!-- Nivel 2 -->
            <col :style="{ width: columnWidths[7] + 'px' }" />
            <!-- Nivel 3 -->
            <col :style="{ width: columnWidths[8] + 'px' }" />
            <!-- Nivel 4 -->
            <col :style="{ width: columnWidths[9] + 'px' }" />
            <!-- Grupo Análisis -->
            <col :style="{ width: columnWidths[10] + 'px' }" />
            <!-- Material -->
            <col :style="{ width: columnWidths[11] + 'px' }" />
            <!-- Cant. Pedido -->
            <col :style="{ width: columnWidths[12] + 'px' }" />
            <!-- Precio Neto -->
            <col :style="{ width: columnWidths[13] + 'px' }" />
            <!-- Temporada -->
            <col :style="{ width: columnWidths[14] + 'px' }" />
            <!-- FOB -->
          </colgroup>
          <thead class="table-light">
            <tr>
              <th class="resizable-th">
                Acción
                <div
                  class="resize-handle"
                  @mousedown="startResize(0, $event)"
                ></div>
              </th>
              <th class="resizable-th">
                Imagen
                <div
                  class="resize-handle"
                  @mousedown="startResize(1, $event)"
                ></div>
              </th>
              <th class="resizable-th">
                Feria
                <div
                  class="resize-handle"
                  @mousedown="startResize(2, $event)"
                ></div>
              </th>
              <th class="resizable-th">
                Madre
                <div
                  class="resize-handle"
                  @mousedown="startResize(3, $event)"
                ></div>
              </th>
              <th class="resizable-th">
                Descripción Madre
                <div
                  class="resize-handle"
                  @mousedown="startResize(4, $event)"
                ></div>
              </th>
              <th class="resizable-th">
                Nivel 1
                <div
                  class="resize-handle"
                  @mousedown="startResize(5, $event)"
                ></div>
              </th>
              <th class="resizable-th">
                Nivel 2
                <div
                  class="resize-handle"
                  @mousedown="startResize(6, $event)"
                ></div>
              </th>
              <th class="resizable-th">
                Nivel 3
                <div
                  class="resize-handle"
                  @mousedown="startResize(7, $event)"
                ></div>
              </th>
              <th class="resizable-th">
                Nivel 4
                <div
                  class="resize-handle"
                  @mousedown="startResize(8, $event)"
                ></div>
              </th>
              <th class="resizable-th">
                Grupo Análisis
                <div
                  class="resize-handle"
                  @mousedown="startResize(9, $event)"
                ></div>
              </th>
              <th class="resizable-th">
                Material
                <div
                  class="resize-handle"
                  @mousedown="startResize(10, $event)"
                ></div>
              </th>
              <th class="resizable-th">
                Cant. Pedido
                <div
                  class="resize-handle"
                  @mousedown="startResize(11, $event)"
                ></div>
              </th>
              <th class="resizable-th">
                Precio Neto
                <div
                  class="resize-handle"
                  @mousedown="startResize(12, $event)"
                ></div>
              </th>
              <th class="resizable-th">
                Temporada
                <div
                  class="resize-handle"
                  @mousedown="startResize(13, $event)"
                ></div>
              </th>
              <th class="resizable-th">FOB</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(sku, index) in skus" :key="index">
              <td class="d-flex gap-1 no-horizontal-border mt-1">
                <button
                  class="btn btn-outline-danger action-btn"
                  @click="eliminarFila(index)"
                  title="Borrar línea"
                >
                  <i class="pi pi-trash me-1"></i> Borrar
                </button>
                <button
                  class="btn btn-sm btn-outline-primary action-btn"
                  @click="abrirSelectorProveedor(sku)"
                  title="Agregar a Proveedor"
                >
                  <i class="pi pi-user-plus me-1"></i> Agregar
                </button>
              </td>
              <td>
                <div class="image-upload-container">
                  <div v-if="sku.imagen" class="image-preview-wrapper">
                    <button
                      class="btn btn-sm btn-outline-info image-btn"
                      @click="verImagen(sku.imagen)"
                    >
                      <i class="pi pi-eye me-1"></i> Ver
                    </button>
                  </div>
                  <button
                    v-else
                    class="btn btn-sm btn-outline-secondary image-btn"
                    @click="triggerFileInput(index)"
                  >
                    <i class="pi pi-upload me-1"></i> Subir
                  </button>
                  <input
                    type="file"
                    :id="'file-input-' + index"
                    class="file-input"
                    accept="image/*"
                    @change="(e) => handleImageUpload(e, sku)"
                  />
                </div>
              </td>
              <td><input v-model="sku.feriaId" class="form-control" /></td>
              <td><input v-model="sku.madreId" class="form-control" /></td>
              <td>
                <input v-model="sku.madreDescripcion" class="form-control" />
              </td>
              <td><input v-model="sku.level1" class="form-control" /></td>
              <td><input v-model="sku.level2" class="form-control" /></td>
              <td><input v-model="sku.level3" class="form-control" /></td>
              <td><input v-model="sku.level4" class="form-control" /></td>
              <td>
                <input v-model="sku.grupoAnalisisDescripcion" class="form-control" />
              </td>
              <td><input v-model="sku.material" class="form-control" /></td>
              <td>
                <input
                  v-model.number="sku.cantidadPedido"
                  type="number"
                  class="form-control"
                />
              </td>
              <td>
                <input
                  v-model.number="sku.precioNetoPedido"
                  type="number"
                  class="form-control"
                />
              </td>
              <td><input v-model="sku.temporada" class="form-control" /></td>
              <td><input v-model="sku.fob" class="form-control" /></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <!-- Modal de selección de proveedor -->
    <SelectProveedorModal
      :visible="mostrarSelectorProveedor"
      :item="skuSeleccionado"
      :proveedores="store.proveedores"
      @seleccionado="asignarSkuAProveedor"
      @close="mostrarSelectorProveedor = false"
    />

    <!-- Modal para visualizar imagen -->
    <div
      v-if="imagenModal.visible"
      class="image-modal-overlay"
      @click="cerrarImagenModal"
    >
      <div class="image-modal-content" @click.stop>
        <img :src="imagenModal.src" class="image-modal-img" />
        <button class="image-modal-close" @click="cerrarImagenModal">
          <i class="pi pi-times"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, watch } from "vue";
import { useCotizacionStore } from "@/stores/cotizacionStore";
import SelectProveedorModal from "@/components/modals/SelectProveedorModal.vue";
import type { Item } from "@/interfaces/ItemsInterface";

const store = useCotizacionStore();
const mostrarSelectorProveedor = ref(false);
const skuSeleccionado = ref<Item | null>(null);
const mostrarTablaSkus = ref(false);

// Cargá los skus desde el store
const skus = ref<Item[]>(Array.isArray(store.skusManuales) ? [...store.skusManuales] : []);

// Estado para el modal de imagen
const imagenModal = ref({
  visible: false,
  src: "",
});

// Estado para el redimensionamiento de columnas
const columnWidths = ref([
  180, // Acción
  120, // Imagen
  100, // Feria
  100, // Madre
  180, // Descripción Madre
  100, // Nivel 1
  100, // Nivel 2
  100, // Nivel 3
  100, // Nivel 4
  120, // Grupo Análisis
  120, // Material
  100, // Cant. Pedido
  100, // Precio Neto
  100, // Temporada
  100, // FOB
]);

const resizing = ref({
  active: false,
  columnIndex: -1,
  startX: 0,
  startWidth: 0,
});

function agregarFila() {
  skus.value.push({
    material: 0,
    cantidadPedido: 0,
    precioNetoPedido: 0,
    level1: "",
    level2: "",
    level3: "",
    level4: "",
    temporada: "",
    textoTemporada: "",
    grupoAnalisisDescripcion: "",
    proveedorCod: "",
    proveedorDescripcion: "",
    fob: 0,
    sku: 0,
    madreId: "",
    feriaId: "",
    madreDescripcion: "",
    imagen: "", // Inicializamos la imagen como vacía
  });
}

function eliminarFila(index: number) {
  skus.value.splice(index, 1);
}

// Funciones para el redimensionamiento de columnas
function startResize(columnIndex: number, event: MouseEvent) {
  event.preventDefault();
  resizing.value = {
    active: true,
    columnIndex,
    startX: event.clientX,
    startWidth: columnWidths.value[columnIndex],
  };

  document.addEventListener("mousemove", handleResize);
  document.addEventListener("mouseup", stopResize);
}

function handleResize(event: MouseEvent) {
  if (!resizing.value.active) return;

  const delta = event.clientX - resizing.value.startX;
  const newWidth = Math.max(50, resizing.value.startWidth + delta); // Mínimo 50px de ancho

  columnWidths.value[resizing.value.columnIndex] = newWidth;
}

function stopResize() {
  resizing.value.active = false;
  document.removeEventListener("mousemove", handleResize);
  document.removeEventListener("mouseup", stopResize);
}

// Función para manejar la carga de imágenes
function handleImageUpload(event: Event, sku: Item) {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    const file = input.files[0];
    const reader = new FileReader();

    reader.onload = (e) => {
      if (e.target && e.target.result) {
        // Guardar la imagen como base64
        sku.imagen = e.target.result as string;
        console.log("Imagen cargada:", sku.imagen);
        console.log("SKU:", sku);
      }
    };

    reader.readAsDataURL(file);
  }
}

function triggerFileInput(index: number) {
  const fileInput = document.getElementById(
    "file-input-" + index
  ) as HTMLInputElement;
  if (fileInput) {
    fileInput.click();
  }
}

function abrirSelectorProveedor(sku: Item) {
  skuSeleccionado.value = sku;
  mostrarSelectorProveedor.value = true;
}

function asignarSkuAProveedor(proveedorId: string) {
  if (skuSeleccionado.value) {
    // En lugar de asignar el SKU a un proveedor, lo agregamos directamente a la cotización
    store.agregarItem(skuSeleccionado.value);
    mostrarSelectorProveedor.value = false;
    skuSeleccionado.value = null;
  }
}

// Funciones para el modal de imagen
function verImagen(src: string) {
  imagenModal.value.src = src;
  imagenModal.value.visible = true;
}

function cerrarImagenModal() {
  imagenModal.value.visible = false;
}

// Cada vez que se modifique la tabla, actualizalo en el store
watch(
  skus,
  (nuevos) => {
    if (typeof store.setSkusManuales === "function") {
      store.setSkusManuales(nuevos);
    } else {
      console.warn("⚠️ setSkusManuales no está definido en el store");
    }
  },
  { deep: true }
);

// Limpiar los event listeners cuando el componente se desmonta
onUnmounted(() => {
  document.removeEventListener("mousemove", handleResize);
  document.removeEventListener("mouseup", stopResize);
});
</script>

<style scoped>
button.custom-hover-outline {
  border: 2px solid #050505;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #ffffff20;
  margin-left: 0.5rem;
  transition: background-color 0.3s ease;
}

button.custom-hover-outline:hover {
  border-color: var(--primary);
  color: var(--primary);
}

.btn-close-table{
  border: 1px solid #050505;
  transition: all 0.3s ease;
}

.btn-close-table:hover{
  border-color: #ff0000;
  color: #ff0000;
}

.table-responsive {
  overflow-x: auto;
  border-radius: 10px;
  background-color: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 1rem;
}

.custom-sku-table {
  border-collapse: collapse;
  width: 100%;
  table-layout: fixed;
  font-size: 0.9rem;
}

.custom-sku-table th {
  background-color: #f8f9fa;
  text-align: center;
  font-weight: 600;
  padding: 0.75rem 0.5rem;
  border-bottom: 2px solid #dee2e6;
  color: #333;
}

/* Estilos para columnas redimensionables */
.resizable-table {
  table-layout: fixed;
}

.resizable-th {
  position: relative;
  overflow: visible;
}

.resize-handle {
  position: absolute;
  top: 0;
  right: 0;
  width: 5px;
  height: 100%;
  cursor: col-resize;
  user-select: none;
  background-color: transparent;
}

.resize-handle:hover,
.resize-handle:active {
  background-color: var(--dark);
  width: 3px;
}

.no-horizontal-border {
  border-top: none !important;
  border-bottom: none !important;
}

/* Estilos para los botones de acción */
.action-btn {
  white-space: nowrap;
  font-size: 0.85rem;
  padding: 0.25rem 0.5rem;
}

/* Estilos para la carga de imágenes */
.image-upload-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 38px; /* Mismo alto que los inputs */
  position: relative;
}

.image-preview-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-btn {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  padding: 0.25rem 0.5rem;
}

.file-input {
  display: none;
}

/* Estilos para el modal de imagen */
.image-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.image-modal-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
}

.image-modal-img {
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
  border: 2px solid white;
  border-radius: 4px;
}

.image-modal-close {
  position: absolute;
  top: -20px;
  right: -20px;
  background-color: white;
  color: black;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 1rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}
</style>
