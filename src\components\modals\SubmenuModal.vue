<template>
  <div v-if="visible" class="submenu-modal-overlay" @click.self="$emit('close')">
    <div class="submenu-modal" :class="`gradient-${type}`">
      <div class="modal-header">
        <h3 class="modal-title text-white">{{ title }}</h3>
        <button class="close-button" @click="$emit('close')">
          <i class="bi bi-x-lg text-white"></i>
        </button>
      </div>
      <div class="modal-body">
        <router-link
          v-for="(item, index) in menuItems"
          :key="index"
          :to="item.route"
          class="submenu-item"
          @click="$emit('close')"
        >
          <i :class="item.icon"></i>
          <span>{{ item.title }}</span>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  visible: boolean;
  title: string;
  type: 'cotizacion' | 'proveedores' | 'auditoria' | 'reportes';
  menuItems: Array<{
    title: string;
    route: string;
    icon: string;
  }>;
}>();

defineEmits<{
  (e: 'close'): void;
}>();
</script>

<style scoped>
.submenu-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.submenu-modal {
  width: 90%;
  min-width: 300px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.modal-title {
  margin: 0;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
  background-color: white;
  padding: 20px;
}

.submenu-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  color: #333;
  text-decoration: none;
  border-radius: 8px;
  margin-bottom: 10px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.submenu-item:hover {
  background-color: #f5f5f5;
  transform: translateX(5px);
}

.submenu-item i {
  margin-right: 12px;
  font-size: 1.2rem;
}

/* Gradientes para los diferentes tipos de modales */
.gradient-cotizacion {
  background: linear-gradient(135deg, #4158D0, #C850C0);
}

.gradient-proveedores {
  background: linear-gradient(135deg, #0093E9, #80D0C7);
}

.gradient-auditoria {
  background: linear-gradient(135deg, #FF9A8B, #FF6A88);
}

.gradient-reportes {
  background: linear-gradient(135deg, #FFB347, #FFCC33);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
