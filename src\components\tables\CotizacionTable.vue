<template>
  <div class="table-wrapper">
    <table class="table">
      <thead>
        <tr>
          <th>ID</th>
          <th>Nombre</th>
          <th>Fecha <PERSON></th>
          <th>Proveedores</th>
          <th>Status</th>
          <th>Acciones</th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="cotizacion in paginatedCotizaciones"
          :key="cotizacion.id"
          class="animate-fade animate-once animate-duration-[1000ms] animate-delay-200"
        >
          <td>{{ cotizacion.id }}</td>
          <td>{{ cotizacion.nombre }}</td>
          <td>{{ new Date(cotizacion.fechaCreacion).toLocaleDateString() }}</td>
          <td>
            <div class="button-container">
              <button
                @click="verProveedores(cotizacion.proveedores)"
                class="btn-ver-proveedores"
              >
                <span class="material-symbols-outlined"> person_search </span>
              </button>
            </div>
          </td>
          <td>{{ cotizacion.tipo }}</td>
          <td>
            <div class="d-flex justify-around align-items-center">
              <!-- Botón "Ver" con ícono -->
              <button
                class="btn-ver"
                @click="$emit('ver-cotizacion', cotizacion)"
              >
                <span class="icon">
                  <!-- Ícono "Ver" -->
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-6"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                    />
                  </svg>
                </span>
              </button>

              <!-- Botón "Actualizar" con ícono -->
              <button
                class="btn-update ml-2"
                v-if="cotizacion.modificada"
                @click="$emit('actualizar', cotizacion.id)"
              >
                <span class="icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-6"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
                    />
                  </svg>
                </span>
              </button>

              <!-- Botón "Eliminar" con ícono -->
              <button
                class="btn-delete ml-2"
                @click="$emit('eliminar', cotizacion.id)"
              >
                <span class="icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-6"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                    />
                  </svg>
                </span>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Paginación -->
  <Paginator
    :rows="pageSize"
    :totalRecords="props.cotizaciones.length"
    :rowsPerPageOptions="[5, 10, 20]"
    @page="onPageChange"
  />
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import type { Cotizacion } from "@/interfaces/CotizacionInterface";
import Paginator from "primevue/paginator";
import Swal from "sweetalert2";

const props = defineProps<{
  cotizaciones: Cotizacion[];
}>();
const emit = defineEmits(["ver-cotizacion", "actualizar", "eliminar"]);

// Paginación
const pageSize = ref(5); // Número de cotizaciones por página
const currentPage = ref(1); // Página actual

const paginatedCotizaciones = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return props.cotizaciones.slice(start, end);
});

const totalPages = computed(() => {
  return Math.ceil(props.cotizaciones.length / pageSize.value);
});

// Función para mostrar el modal con los proveedores
function verProveedores(proveedores: any[]) {
  // Crear un texto que contenga todos los proveedores
  const proveedoresList = proveedores
    .map((p) => {
      return p.tipo === "existente" ? p.companyName : p.email;
    })
    .join("<br>");

  // Mostrar un modal con SweetAlert2
  Swal.fire({
    title: "Proveedores de la Cotización",
    html: proveedoresList, // Mostrar la lista de proveedores
    icon: "info",
    confirmButtonText: "Cerrar",
  });
}

// Funciones para cambiar de página
function onPageChange(event: any) {
  currentPage.value = event.page + 1; // PrimeVue cuenta las páginas desde 0
  pageSize.value = event.rows;
}
</script>

<style scoped>
.table-wrapper {
  overflow-x: auto;
  padding: 0.5rem;
  max-height: 53vh;
  display: row;
  justify-content: center; /* Centrar horizontalmente */
  align-items: center; /* Centrar verticalmente, si es necesario */
}

.table th,
.table td {
  padding: 0.75rem;
  text-align: center;
  align-items: center;
}

.btn-ver-proveedores {
  padding: 0.4rem 0.75rem;
  cursor: pointer;
}

.btn-ver-proveedores span {
  margin-right: 8px; /* Espacio entre el ícono y el texto */
  font-size: 1.4rem; /* Tamaño del ícono */
}

.btn-ver,
.btn-update,
.btn-delete {
  cursor: pointer;
}
</style>
