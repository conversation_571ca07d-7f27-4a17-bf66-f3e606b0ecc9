<template>
  <div class="container-fluid min-vh-100">
    <UserHeader title="Nueva Cotización" icon="file-earmark-plus" />

    <main class="container-fluid rounded shadow-xl py-4 px-4 animate-fade">
      <!-- Instrucciones para crear una cotización -->
      <div class="welcome-section mb-4">
        <h3 class="mb-3">Crear Nueva Cotización</h3>
        <p class="lead">Para crear una nueva cotización, siga estos pasos:</p>

        <ol class="instructions">
          <li>Haga clic en <strong>"Agregar Proveedor/MadreId"</strong> para seleccionar los proveedores y materiales.</li>
          <li>Una vez agregados, podrá ver y gestionar sus cotizaciones en <strong>"Ver Cotizaciones en Proceso"</strong>.</li>
          <li>Desde allí, podrá seleccionar las cotizaciones que desee enviar.</li>
        </ol>

        <div class="alert alert-info mt-4">
          <i class="pi pi-info-circle me-2"></i>
          Las cotizaciones se crearán automáticamente al seleccionar proveedores y materiales.
        </div>
      </div>

      <!-- Botones de navegación a la izquierda -->
      <div class="buttons-left">
        <GradientButton @click="navigateTo('filtroSeleccion')" class="option-button">
          <i class="pi pi-database me-2"></i> Agregar Proveedor/MadreId
        </GradientButton>

        <GradientButton @click="navigateTo('listaCotizaciones')" class="option-button">
          <i class="pi pi-list me-2"></i> Ver Cotizaciones en Proceso
        </GradientButton>
      </div>

      <!-- Botones inferiores a la derecha -->
      <div class="bottom-buttons">
        <GradientButton @click="navigateTo('Home')" class="nav-button">
          <i class="pi pi-home me-2"></i> Menú Inicial
        </GradientButton>
      </div>

    </main>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import UserHeader from "@/components/layout/headers/header.vue";
import GradientButton from "@/components/ui/GradientButton.vue";
import keycloak from "@/auth/keycloak";
import { useAuth } from "@/composables/useAuth";
import { jwtDecode } from "jwt-decode";
import { onMounted } from 'vue';
// validación de permiso de acceso a ruta según role
const { hasRole } = useAuth();
const token = keycloak.token;
if (token) {
  const decoded: any = jwtDecode(token);
  console.log("🧾 Token Decodificado:", decoded);
  console.log("Has roles:", decoded.resource_access?.myclient?.roles);
}

const allowedRoles = ["Sourcing", "Auditor", "Diseño"];

onMounted(() => {
  const isAuthorized = allowedRoles.some((role) => hasRole(role));

  if (!isAuthorized) {
    router.push({ name: "Home" });
  }
});

const router = useRouter();

// Función para navegar a diferentes vistas
function navigateTo(route: string) {
  router.push({ name: route });
}

</script>

<style scoped>
input:hover{
  border-color: var(--secondary);
  box-shadow: 0 0 0 0.2rem rgba(255, 188, 34, 0.25);
}

.container-fluid {
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.519);
}

.resumen-section {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 2px 4px 6px rgba(0, 0, 0, 0.253);
}

.resumen-section:hover{
  box-shadow: 2px 4px 6px rgba(184, 181, 10, 0.553);
}

.resumen-info {
  display: flex;
  align-items: center;
}

.badge {
  font-size: 0.9rem;
  padding: 0.5rem 0.75rem;
}

/* Estilos para los contenedores de botones */
.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 1.5rem 0;
}

.centered-btn {
  min-width: 250px;
}

/* Estilos para los botones a la izquierda */
.buttons-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  margin: 1rem 0;
  width: 100%;
  max-width: 350px;
}


.option-button:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.option-button i {
  font-size: 1.5rem;
  margin-right: 0.75rem;
}

/* Estilos para los botones inferiores a la derecha */
.bottom-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  width: 100%;
}

.nav-button {
  min-width: 150px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1rem;
  transition: all 0.2s ease;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .buttons-left {
    max-width: 100%;
  }

  .bottom-buttons {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
