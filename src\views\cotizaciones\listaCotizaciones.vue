<template>
  <div class="container-fluid min-vh-100">
    <!-- <UserHeader title="Cotizaciones en Proceso" icon="list-check" /> -->

    <main class="container-fluid rounded shadow-xl py-4 px-4 animate-fade">
      <!-- Lista de cotizaciones en proceso -->
      <div class="cotizaciones-section mb-4">
        <h3 class="mb-3">Cotizaciones Guardadas</h3>

        <div
          v-if="store.cotizacionesEnProceso.length === 0"
          class="alert alert-info"
        >
          No hay cotizaciones en proceso. Cree una nueva cotización para
          comenzar.
        </div>

        <div v-else>
          <!-- <div class="mb-3">
            <button class="btn btn-outline-primary" @click="seleccionarTodas">
              <i class="pi pi-check-square me-2"></i> Selecciona<PERSON>
            </button>
            <button
              class="btn btn-outline-secondary ms-2"
              @click="deseleccionarTodas"
            >
              <i class="pi pi-square me-2"></i> Deselecciona<PERSON>
            </button>
          </div> -->

          <div class="table-responsive">
            <table class="table table-striped table-hover tabla-con-bordes">
              <thead>
                <tr>
                  <!-- <th>Seleccionar</th> -->
                  <th>Nombre</th>
                  <th>Fecha de Creación</th>
                  <th>Grupo de Análisis</th>
                  <th>Proveedores</th>
                  <th>Madre</th>
                  <th>SKU's</th>
                  <th>Acciones</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(cotizacion, index) in store.cotizacionesEnProceso"
                  :key="cotizacion.id"
                >
                  <!-- <td>
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        :id="'cotizacion-' + index"
                        :checked="estaSeleccionada(cotizacion)"
                        @change="toggleSeleccion(cotizacion)"
                      />
                    </div>
                  </td> -->
                  <td>
                    <div class="d-flex align-items-center">
                      <span>{{ cotizacion.quotation }}</span>
                      <button class="btn btn-sm btn-link ms-2" @click="editarNombreCotizacion(cotizacion, index)">
                        <i class="pi pi-pencil"></i>
                      </button>
                    </div>
                  </td>
                  <td>{{ formatDate(cotizacion.fechaCreacion) }}</td>
                  <td>{{ [...new Set(cotizacion.items.map(i => i.grupoAnalisisDescripcion))].join(' | ') }}</td>
                  <td>{{ cotizacion.suppliers.map(p => p.companyName).join(' | ') }}</td>
                  <td>{{ [...new Set(cotizacion.items.map(i => i.madreDescripcion))].join(' | ') }}</td>
                  <td>{{ cotizacion.items.map(i => i.sku).join(' | ') }}</td>
                  <td>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-success me-2" @click="enviarCotizacionIndividual(cotizacion  /*, index */)">
                        <i class="pi pi-send"></i> Enviar
                      </button>
                        <button label="btn btn-sm btn-primary me-2" @click="descargarPDF(cotizacion)">
                          <i class="pi pi-download"></i> PDF
                        </button>

                        <!-- <button label="btn btn-sm btn-primary me-2" @click="descargaExcelCotización(cotizacion)">
                          <i class="pi pi-download"></i> Excel
                        </button> -->

                      <button class="btn btn-sm btn-primary me-2" @click="verDetalleCotizacion(cotizacion)">
                        <i class="pi pi-eye"></i>
                      </button>

                      <button
                        class="btn btn-sm btn-outline-danger"
                        @click="eliminarCotizacion(index)"
                      >
                        <i class="pi pi-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <!-- Botones de acción -->
        <!-- <div class="d-flex justify-content-end mt-3">
          <button
            class="btn btn-primary me-2"
            @click="navigateTo('cotizacionesSeleccionadas')"
            :disabled="store.cotizacionesSeleccionadas.length === 0"
          >
            <i class="pi pi-check me-2"></i> Continuar con Seleccionadas ({{
              store.cotizacionesSeleccionadas.length
            }})
          </button>
        </div> -->
      </div>

      <!-- Botones inferiores a la derecha -->
      <div class="bottom-buttons">
        <div class="button-container">

          <GradientButton
            @click="navigateTo('filtroSeleccion')"
            class="nav-button">
            <i class="pi pi-arrow-left me-2"></i> Volver a Crear Cotización
          </GradientButton>
        </div>
        <div class="button-container">
          <GradientButton
            @click="navigateTo('nuevaCotizacion')"
            class="nav-button"
          >
            <i class="pi pi-file-plus me-2"></i> Volver a Nueva Cotización
          </GradientButton>
        </div>
        <div class="button-container">
          <GradientButton @click="navigateTo('Home')" class="nav-button">
            <i class="pi pi-home me-2"></i> Menú Inicial
          </GradientButton>
        </div>
      </div>
    </main>

    <!-- Modal para envío de correo -->
    <ModalEnvioCorreoProv
      :isVisible="showEmailModal"
      :cotizacion="cotizacionSeleccionada"
      @close="handleCloseEmailModal"
      @email-sent="handleEmailSent"
    />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
// import UserHeader from "@/components/layout/headers/header.vue";
import GradientButton from "@/components/ui/GradientButton.vue";
import { useCotizacionStore } from "@/stores/cotizacionStore";
import Swal from "sweetalert2";
import type { Cotizacion } from "@/interfaces/CotizacionInterface";
import { onMounted, ref } from "vue";
import { enviarCotizacion } from "@/services/cotizacionService";
import ModalEnvioCorreoProv from "@/components/modals/ModalEnvioCorreoProv.vue";
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

// import * as XLSX from 'xlsx';


const router = useRouter();
const store = useCotizacionStore();

// Estado para el modal de envío de correo
const showEmailModal = ref(false);
const cotizacionSeleccionada = ref<Cotizacion | null>(null);





onMounted(() => {
      console.log('🔄 Cotizaciones en proceso al montar:', store.cotizacionesEnProceso);

      // Puedes ejecutar aquí cualquier lógica con ese valor
      if (store.cotizacionesEnProceso.length > 0) {
        // Por ejemplo, mostrar un toast, hacer una acción, redirigir, etc.
      }
    });

// Función para formatear fechas
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString();
}

// Verificar si una cotización está seleccionada
// function estaSeleccionada(cotizacion: Cotizacion): boolean {
//   return store.cotizacionesSeleccionadas.some((c) => c.id === cotizacion.id);
// }

// // Alternar la selección de una cotización
// function toggleSeleccion(cotizacion: Cotizacion): void {
//   if (estaSeleccionada(cotizacion)) {
//     const index = store.cotizacionesSeleccionadas.findIndex(
//       (c) => c.id === cotizacion.id
//     );
//     if (index !== -1) {
//       store.deseleccionarCotizacion(index);
//     }
//   } else {
//     store.seleccionarCotizacion(cotizacion);
//   }
// }

// Eliminar una cotización
function eliminarCotizacion(index: number): void {
  Swal.fire({
    title: "¿Está seguro?",
    text: "Esta acción no se puede deshacer",
    icon: "warning",
    showCancelButton: true,
    confirmButtonText: "Sí, eliminar",
    cancelButtonText: "Cancelar",
  }).then((result) => {
    if (result.isConfirmed) {
      store.eliminarCotizacionEnProceso(index);
      Swal.fire("¡Eliminada!", "La cotización ha sido eliminada", "success");
    }
  });
}

// Seleccionar todas las cotizaciones
// function seleccionarTodas(): void {
//   store.cotizacionesEnProceso.forEach((cotizacion) => {
//     if (!estaSeleccionada(cotizacion)) {
//       store.seleccionarCotizacion(cotizacion);
//     }
//   });

//   Swal.fire({
//     title: "Cotizaciones seleccionadas",
//     text: `Se han seleccionado ${store.cotizacionesSeleccionadas.length} cotizaciones`,
//     icon: "success",
//     timer: 1500,
//     showConfirmButton: false,
//   });
// }

// // Deseleccionar todas las cotizaciones
// function deseleccionarTodas(): void {
//   // Deseleccionamos todas las cotizaciones
//   while (store.cotizacionesSeleccionadas.length > 0) {
//     store.deseleccionarCotizacion(0);
//   }

//   Swal.fire({
//     title: "Cotizaciones deseleccionadas",
//     text: "Se han deseleccionado todas las cotizaciones",
//     icon: "info",
//     timer: 1500,
//     showConfirmButton: false,
//   });
// }

// Editar el nombre de una cotización
function editarNombreCotizacion(cotizacion: Cotizacion, index: number): void {
  Swal.fire({
    title: 'Editar nombre de la cotización',
    input: 'text',
    inputValue: cotizacion.quotation,
    showCancelButton: true,
    confirmButtonText: 'Guardar',
    cancelButtonText: 'Cancelar',
    inputValidator: (value) => {
      if (!value.trim()) {
        return 'El nombre no puede estar vacío';
      }
      return null;
    }
  }).then((result) => {
    if (result.isConfirmed && result.value) {
      // Actualizar el nombre de la cotización
      store.cotizacionesEnProceso[index].quotation = result.value;

      // Si la cotización está seleccionada, actualizar también en cotizacionesSeleccionadas
      const indexSeleccionada = store.cotizacionesSeleccionadas.findIndex(c => c.id === cotizacion.id);
      if (indexSeleccionada !== -1) {
        store.cotizacionesSeleccionadas[indexSeleccionada].quotation = result.value;
      }

      Swal.fire({
        title: 'Nombre actualizado',
        text: `La cotización ahora se llama "${result.value}"`,
        icon: 'success',
        timer: 1500,
        showConfirmButton: false
      });
    }
  });
}

// Ver detalle de una cotización
function verDetalleCotizacion(cotizacion: Cotizacion): void {
  let itemsHtml = '';

  if (cotizacion.items.length > 0) {
    itemsHtml = '<table class="table table-sm mt-3">' +
      '<thead><tr><th>Madre ID</th><th>Madre</th><th>Grupo de Análisis</th><th>SKU</th><th>Proveedor ID</th> <th>Proveedor</th></tr></thead>' +
      '<tbody>';

    cotizacion.items.forEach(item => {
      itemsHtml += `<tr>
        <td>${item.madreId || 'N/A'}</td>
        <td>${item.madreDescripcion || 'Sin descripción'}</td>
        <td>${item.grupoAnalisisDescripcion || 'Sin descripción'}</td>
        <td>${item.sku || 'N/A'}</td>
        <td>${cotizacion.suppliers.map(p => p.supplierId).join('  ') || 'N/A'}</td>
        <td>${cotizacion.suppliers.map(p => p.companyName).join(' | ') || 'No especificado'}</td>
      </tr>`;
    });

    itemsHtml += '</tbody></table>';
  } else {
    itemsHtml = '<div class="alert alert-info mt-3">Esta cotización no tiene materiales.</div>';
  }

  let proveedoresHtml = '';

  if (cotizacion.suppliers.length > 0) {
    proveedoresHtml = '<div class="mt-3"><strong>Proveedores:</strong> ';
    proveedoresHtml += cotizacion.suppliers.map(p => p.companyName || p.email || 'Sin nombre').join(', ');
    proveedoresHtml += '</div>';
  } else {
    proveedoresHtml = '<div class="alert alert-info mt-3">Esta cotización no tiene proveedores.</div>';
  }

  Swal.fire({
    title: `Detalle de cotización: ${cotizacion.quotation}`,
    html: `
      <div class="text-start">
        <p><strong>Fecha de creación:</strong> ${formatDate(cotizacion.fechaCreacion)}</p>
        ${proveedoresHtml}
        ${itemsHtml}
      </div>
    `,
    width: '80%',
    confirmButtonText: 'Cerrar'
  });
}

// Función para navegar a diferentes vistas
function navigateTo(route: string) {
  router.push({ name: route });
}

async function enviarCotizacionIndividual(cotizacion: Cotizacion): Promise<void> {

  Swal.fire({
    title: `Enviando cotización "${cotizacion.quotation}"`,
    text: "Por favor espere...",
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });

  try {
    console.log(`Enviando cotización individual ${cotizacion.quotation} al backend...`);
    await enviarCotizacion(cotizacion);
    console.log(`Cotización ${cotizacion.quotation} enviada con éxito`);

    // Mostrar mensaje de éxito
    Swal.fire({
      title: "¡Éxito!",
      text: `La cotización "${cotizacion.quotation}" ha sido enviada correctamente. ¿Desea enviar un correo al proveedor?`,
      icon: "success",
      showCancelButton: true,
      confirmButtonText: "Sí, enviar correo",
      cancelButtonText: "No, finalizar"
    }).then((result) => {
      if (result.isConfirmed) {
        // Guardar la cotización seleccionada y mostrar el modal de correo
        cotizacionSeleccionada.value = cotizacion;
        showEmailModal.value = true;
      } else {
        // Si el usuario no quiere enviar correo, eliminar la cotización de la lista
        eliminarCotizacionProcesada(cotizacion);
      }
    });
  } catch (error) {
    console.error(`Error al enviar la cotización ${cotizacion.quotation}:`, error);
    Swal.fire({
      title: "Error",
      text: `Hubo un problema al enviar la cotización "${cotizacion.quotation}"`,
      icon: "error",
      confirmButtonText: "Entendido"
    });
  }
}

// Función para eliminar una cotización después de procesarla
function eliminarCotizacionProcesada(cotizacion: Cotizacion): void {
  const indexEnProceso = store.cotizacionesEnProceso.findIndex(c => c.id === cotizacion.id);
  if (indexEnProceso !== -1) {
    store.eliminarCotizacionEnProceso(indexEnProceso);
  }
}

// Función para manejar el cierre del modal
function handleCloseEmailModal(): void {
  showEmailModal.value = false;

  // Si hay una cotización seleccionada, eliminarla de la lista
  if (cotizacionSeleccionada.value) {
    eliminarCotizacionProcesada(cotizacionSeleccionada.value);
    cotizacionSeleccionada.value = null;
  }
}

// Función para manejar el envío exitoso del correo
function handleEmailSent(): void {
  if (cotizacionSeleccionada.value) {
    Swal.fire({
      title: "¡Correo enviado!",
      text: `Se ha enviado un correo al proveedor con la cotización "${cotizacionSeleccionada.value.quotation}"`,
      icon: "success",
      timer: 2000,
      showConfirmButton: false
    });

    // Eliminar la cotización de la lista y cerrar el modal
    eliminarCotizacionProcesada(cotizacionSeleccionada.value);
    showEmailModal.value = false;
    cotizacionSeleccionada.value = null;
  }
}

function descargarPDF(cotizacion: Cotizacion): void {
  const cotizaciones = [cotizacion];
  usePDF(cotizaciones, `${cotizacion.quotation}.pdf`);
}

// function descargarExcel(cotizacion: Cotizacion): void {
//   const cotizaciones = [cotizacion];
//   useExcel(cotizaciones, `${cotizacion.nombre}.xlsx`);
// }



function usePDF(datos: any[], nombreArchivo: string = 'reporte.pdf') {
  const doc = new jsPDF();

  doc.setFontSize(18);
  doc.text(datos[0].nombre + ' - ' + datos[0].fechaCreacion, 14, 22);

  autoTable(doc, {
    startY: 30,
    head: [['ID', 'Madre ID', 'Madre', 'Grupo de Análisis', 'Proveedor ID', 'Proveedor']],
    body: datos.map((item) => [
      item.id,
      item.items.map((i: any) => i.madreId).join(' | ') || 'N/A',
      item.items.map((i: any) => i.madreDescripcion).join(' | ') || 'N/A',
      item.items.map((i: any) => i.grupoAnalisisDescripcion).join(' | ') || 'N/A',
      item.proveedores[0].supplierId || 'N/A',
      item.proveedores[0].companyName || 'N/A'
    ]),
  });

  doc.save(nombreArchivo);
}

</script>

<style scoped>
.container-fluid {
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.519);
}

.cotizaciones-section {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 2px 4px 6px rgba(0, 0, 0, 0.253);
}

.cotizaciones-section:hover {
  box-shadow: 2px 4px 6px rgba(184, 181, 10, 0.553);
}

.bottom-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  width: 100%;
}

.nav-button {
  min-width: 150px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1rem;
  transition: all 0.2s ease;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .bottom-buttons {
    flex-direction: column;
    align-items: stretch;
  }
}

.tabla-con-bordes th,
.tabla-con-bordes td {
  border-right: 1px solid #dee2e6;
}

.table-responsive {
  border-radius: 10px;
}

.tabla-con-bordes th:last-child,
.tabla-con-bordes td:last-child {
  border-right: none;
}

.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}
</style>
