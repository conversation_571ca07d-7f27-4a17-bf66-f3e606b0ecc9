<template>
  <div class="relacion-container">
    <div class="relacion-header">
      <h5 class="fw-bold">
        <i class="pi pi-sitemap me-2"></i> Proveedores relacionados con Madre
      </h5>
      <div class="madre-info">
        <span class="badge bg-info">Madre ID: {{ mapeoBackend.madreId }}</span>
        <span class="badge bg-secondary">Descripción: {{ mapeoBackend.madreDescripcion }}</span>
      </div>
    </div>

    <div class="relacion-content">
      <div v-if="cargando" class="text-center py-4">
        <div class="spinner-border text-warning" role="status">
          <span class="visually-hidden">Cargando...</span>
        </div>
        <p class="mt-2">Buscando proveedores relacionados...</p>
      </div>

      <div v-else-if="!cargando && proveedoresUnicosComputados.length == 0" class="text-center py-4">
        <i
          class="pi pi-exclamation-triangle text-warning"
          style="font-size: 2rem"
        ></i>
        <p class="mt-2">No se encontraron proveedores relacionados con esta Madre.</p>
      </div>

      <div v-else class="proveedores-list">
        <div class="list-header">
          <div>
            <span class="text-muted">Seleccione uno o más proveedores</span>
          </div>
          <span class="badge bg-primary">{{ proveedoresUnicosComputados.length }} proveedores</span>
        </div>

        <div class="list-body">
          <div
            v-for="(proveedor, index) in proveedoresUnicosComputados"
            :key="proveedor.proveedorCod"
            class="proveedor-item"
          >
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                :id="`proveedor-${index}`"
                :checked="proveedoresSeleccionados.includes(index)"
                @change="toggleProveedor(index)"
              />
              <label class="form-check-label" :for="`proveedor-${index}`">
                {{ proveedor.proveedorDescripcion }}
              </label>
            </div>
            <div class="proveedor-badges">
              <span v-if="proveedor.proveedorCod || proveedor._esNuevoProveedor" class="badge bg-light text-dark">Creditor: {{ proveedor.proveedorCod }}</span>
              <span v-if="proveedor._esNuevoProveedor" class="badge bg-success text-white">N/R</span>
              <!-- <span v-if="proveedor.level1" class="badge bg-info text-dark">{{ proveedor.level1 }}</span>
              <span v-if="proveedor.level2" class="badge bg-secondary text-white">{{ proveedor.level2 }}</span>
              <span v-if="proveedor.level3" class="badge bg-warning text-dark">{{ proveedor.level3 }}</span> -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="relacion-footer">
      <div>
        <button
            class="btn btn-success me-2"
            @click="openModal"
            title="Agregar proveedor no relacionado"
        >
            <i class="pi pi-plus"></i> Agregar proveedor no relacionado
        </button>

        <ModalProveedorNoRelacionado
            :isVisible="showModal"
            :madreId="mapeoBackend.madreId"
            :descripcionMadre="mapeoBackend.madreDescripcion"
            :proveedoresRelacionados="proveedoresUnicosComputados"
            @close="closeModal"
            @supplier-added="handleSupplierAdded"
        />
    </div>
      <button
        class="btn btn-primary"
        @click="emitirProveedoresSeleccionados"
        :disabled="!hayProveedoresSeleccionados"
      >
        <i class="pi pi-check me-2"></i> Agregar proveedores seleccionados
        <span v-if="proveedoresSeleccionados.length > 0" class="badge bg-light text-dark ms-2">
          {{ proveedoresSeleccionados.length }}
        </span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import type { Item } from "@/interfaces/ItemsInterface";
import { RelacionesService } from "@/services/relacionesService";
import ModalProveedorNoRelacionado from "../modals/ModalProveedorNoRelacionado.vue";
import Swal from 'sweetalert2';

const showModal = ref(false);

function openModal() {
    showModal.value = true;
}

function closeModal() {
    showModal.value = false;
}

function handleSupplierAdded(supplier: ItemExtendido) {
    console.log('Proveedor agregado:', supplier);

  // Verificar si el proveedor ya existe en la lista
  const yaExiste = proveedoresUnicosComputados.value.some(
    p => p.proveedorCod === supplier.proveedorCod
  );

  if (yaExiste) {
    // Mostrar mensaje de que el proveedor ya existe
    Swal.fire({
      title: 'Proveedor duplicado',
      text: 'Este proveedor ya está relacionado con esta madre',
      icon: 'warning',
      confirmButtonText: 'Entendido'
    });
    return;
  }

  // Agregar el nuevo proveedor a la lista de proveedores relacionados
  if (supplier._esNuevoProveedor) {
      // Asegurarse de que el proveedor tenga la información de la madre
      supplier.madreId = props.mapeoBackend.madreId;
      supplier.madreDescripcion = props.mapeoBackend.madreDescripcion;

      // Agregar el nuevo proveedor al inicio de la lista para que sea más visible
      proveedoresRelacionados.value.unshift(supplier);

      // Seleccionar automáticamente el nuevo proveedor
      proveedoresSeleccionados.value = [0];

      Swal.fire({
      title: 'Proveedor agregado',
      text: `Se ha agregado el proveedor ${supplier.proveedorDescripcion}`,
      icon: 'success',
      timer: 2000,
      showConfirmButton: false
    });
  }
};


const cargando = ref(true);

const props = defineProps<{
  mapeoBackend: Item;
}>();

const emit = defineEmits<{
  (e: "seleccionarProveedores", proveedores: Item[]): void;
}>();

// Definir un tipo extendido para los proveedores que pueden incluir propiedades adicionales
type ItemExtendido = Item & { _esNuevoProveedor?: boolean; _email?: string };

const proveedoresRelacionados = ref<ItemExtendido[]>([]);
// Usamos un array para almacenar los índices de los proveedores seleccionados
const proveedoresSeleccionados = ref<number[]>([]);

// Propiedades computadas para filtrar duplicados
const proveedoresUnicosComputados = computed(() => {
  // Usar un Map para mantener solo la última ocurrencia de cada proveedorCod
  const proveedoresMap = new Map();

  // Si no hay proveedores relacionados, devolver un array vacío
  if (!proveedoresRelacionados.value || !Array.isArray(proveedoresRelacionados.value)) {
    console.warn("proveedoresRelacionados no es un array válido");
    return [];
  }

  // Iterar sobre los proveedores en orden inverso para mantener el último
  for (let i = proveedoresRelacionados.value.length - 1; i >= 0; i--) {
    const proveedor = proveedoresRelacionados.value[i];
    if (proveedor && proveedor.proveedorCod) {
      proveedoresMap.set(proveedor.proveedorCod, proveedor);
    }
  }

  // Convertir el Map de vuelta a un array
  return Array.from(proveedoresMap.values());
});

// Función para cargar proveedores relacionados con la Madre
// async function cargarProveedoresRelacionados() {
//   try {
//     cargando.value = true;
//     // Resetear selección
//     proveedorSeleccionado.value = -1;

//     if (props.mapeoBackend.madreId) {
//       // Obtener proveedores relacionados con la madre
//       proveedoresRelacionados.value = await RelacionesService.obtenerProveedoresRelacionados(props.mapeoBackend.madreId);
//       console.log("Proveedores relacionados cargados:", proveedoresRelacionados.value);
//     } else {
//       proveedoresRelacionados.value = [];
//     }
//   } catch (error) {
//     console.error("Error al cargar proveedores relacionados:", error);
//     proveedoresRelacionados.value = [];
//   } finally {
//     cargando.value = false;
//   }
// }

async function cargarProveedoresRelacionados() {
  try {
    cargando.value = true;
    // Resetear selección
    proveedoresSeleccionados.value = [];

    if (props.mapeoBackend && props.mapeoBackend.madreDescripcion) {
      console.log("Buscando proveedores para madre:", props.mapeoBackend.madreDescripcion);

      // Obtener proveedores relacionados con la madre
      const resultado = await RelacionesService.obtenerProveedoresRelacionados(
        props.mapeoBackend.madreDescripcion
      );

      console.log("Resultado de la búsqueda:", resultado);

      // Verificar si el resultado es válido
      if (Array.isArray(resultado)) {
        // Eliminar duplicados basados en proveedorCod
        const proveedoresUnicos = eliminarDuplicados(resultado, 'proveedorCod');
        proveedoresRelacionados.value = proveedoresUnicos;

        console.log(`Se encontraron ${resultado.length} proveedores, ${proveedoresUnicos.length} únicos`);
      } else {
        console.warn("El resultado no es un array:", resultado);
        proveedoresRelacionados.value = [];
      }
    } else {
      console.log("No hay descripción de madre para buscar proveedores");
      proveedoresRelacionados.value = [];
    }
  } catch (error) {
    console.error("Error al cargar proveedores relacionados:", error);
    proveedoresRelacionados.value = [];
  } finally {
    cargando.value = false;
  }
}

function eliminarDuplicados(array: any[], prop: string) {
  const seen = new Set();
  return array.filter(item => {
    const value = item[prop];
    if (seen.has(value)) {
      return false;
    }
    seen.add(value);
    return true;
  });
}

// Cargar datos al montar el componente
onMounted(cargarProveedoresRelacionados);

// Observar cambios en las propiedades para recargar los datos
watch(
  () => props.mapeoBackend.madreId,
  (nuevoMadreId) => {
    if (nuevoMadreId) {
      console.log("Nuevo madreId detectado:", nuevoMadreId);
      cargarProveedoresRelacionados();
    }
  }
);

// Función para alternar la selección de un proveedor
function toggleProveedor(index: number) {
  const indexEnSeleccion = proveedoresSeleccionados.value.indexOf(index);

  if (indexEnSeleccion > -1) {
    // Si ya está seleccionado, lo removemos
    proveedoresSeleccionados.value.splice(indexEnSeleccion, 1);
  } else {
    // Si no está seleccionado, lo agregamos
    proveedoresSeleccionados.value.push(index);
  }

  console.log("Proveedores seleccionados:", proveedoresSeleccionados.value);
}

// Computed para verificar si hay proveedores seleccionados
const hayProveedoresSeleccionados = computed(() => {
  return proveedoresSeleccionados.value.length > 0;
});

// Función para emitir los proveedores seleccionados
function emitirProveedoresSeleccionados() {
  if (proveedoresSeleccionados.value.length > 0) {
    const proveedoresSeleccionadosObj = proveedoresSeleccionados.value.map(index =>
      proveedoresRelacionados.value[index]
    );
    emit("seleccionarProveedores", proveedoresSeleccionadosObj);
  }
}
</script>

<style scoped>
.relacion-container {
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.relacion-header {
  background-color: #e9ecef;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.madre-info {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.relacion-content {
  padding: 1rem;
  flex-grow: 1;
  overflow-y: auto;
  max-height: 300px;
}

.relacion-footer {
  padding: 1rem;
  background-color: #e9ecef;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: center;
}

.proveedores-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #e9ecef;
  border-radius: 5px;
}

.list-body {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.proveedor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.proveedor-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.proveedor-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.badge {
  font-size: 0.6rem;
  padding: 0.2rem 0.2rem;
}
</style>
