<template>
  <!-- SECCIÓN 1: BUSCAR PROVEEDORES/SKUS -->
  <div class="d-container mb-4">
    <div class="d-flex align-items-center mb-3">
      <i class="pi pi-users me-2"></i>
      <h5 class="fw-bold mb-0">Buscar</h5>
    </div>

    <div class="mb-4">
      <label class="form-label fw-semibold">Buscar por:</label>
      <div class="d-flex gap-2">
        <select v-model="tipoBusqueda" class="form-select">
          <option disabled value="">Seleccione una opción</option>
          <option
            v-for="filtro in filtros"
            :key="filtro.id"
            :value="filtro.valor"
          >
            {{ filtro.etiqueta }}
          </option>
        </select>
        <input
          v-model="valorBusqueda"
          class="form-control"
          placeholder="Ingrese un valor"
          @keyup.enter="agregarFiltro"
        />
      </div>
      <div class="d-flex align-items-center justify-evenly mt-3">
        <button
          v-if="Object.keys(filtrosSeleccionados).length"
          class="btn btn-outline-danger"
          @click="limpiarFiltros"
        >
          Limpiar Filtros
        </button>
        <button class="btn btn-outline-primary" @click="agregarFiltro">
          Agregar Filtro
        </button>
      </div>
    </div>

    <!-- Filtros aplicados -->
    <div
      class="filtros-aplicados mb-4"
      v-if="Object.keys(filtrosSeleccionados).length"
    >
      <h6 class="fw-bold mb-3">Filtros aplicados:</h6>
      <div class="filtros-tags">
        <div
          v-for="(valor, campo) in filtrosSeleccionados"
          :key="campo"
          class="filtro-tag"
        >
          <span class="filtro-nombre">{{ obtenerEtiquetaFiltro(campo) }}</span>
          <span class="filtro-valor">"{{ valor }}"</span>
          <button
            class="btn-remove"
            @click="delete filtrosSeleccionados[campo]"
          >
            <i class="pi pi-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Botón de búsqueda centralizado -->
    <div class="text-center mb-4">
      <button
        class="btn btn-search"
        @click="handleBuscar"
        :disabled="!Object.keys(filtrosSeleccionados).length"
      >
        <i class="pi pi-search"></i>
      </button>
    </div>
    <small v-if="sinResultados" class="text-danger mt-1 d-block text-center">
      No se encontraron resultados para tu búsqueda.
    </small>

    <div class="flex flex-col items-center mb-4">
      <button
        v-if="resultados.length"
        class="btn mt-3 btn-outline-danger"
        @click="limpiarResultados"
      >
        Cerrar Tabla
      </button>
    </div>

    <div ref="tablaRef" v-if="resultados.length">
      <h6 class="fw-semibold mb-3">Resultados:</h6>
      <ResultadosTabla
        :resultados="resultados"
        @agregarProveedor="agregarProveedorDesdeItem"
        @seleccionarProveedorParaSku="abrirSelectorParaSku"
      />
      <SelectProveedorModal
        :visible="mostrarSelectorProveedor"
        :item="skuSeleccionado"
        :proveedores="store.proveedores"
        @seleccionado="agregarSkuAlProveedorSeleccionado"
        @close="mostrarSelectorProveedor = false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick } from "vue";
import { useFiltroBusqueda } from "@/composables/useFiltroBusqueda";
import ResultadosTabla from "@/components/tables/SkuTable.vue";
import SelectProveedorModal from "@/components/modals/SelectProveedorModal.vue";
import type { Item } from "@/interfaces/ItemsInterface";
import { useCotizacionStore } from "@/stores/cotizacionStore";
import type { Proveedor } from "@/interfaces/ProveedorInterface";

const skuSeleccionado = ref<Item | null>(null);
const mostrarSelectorProveedor = ref(false);
const store = useCotizacionStore();
const tablaRef = ref<HTMLElement | null>(null);

// Composable para la búsqueda
const {
  tipoBusqueda,
  filtros,
  valorBusqueda,
  filtrosSeleccionados,
  resultados,
  sinResultados,
  agregarFiltro,
  obtenerEtiquetaFiltro,
  realizarBusqueda,
  limpiarResultados,
  obtenerFiltrosDisponibles,
} = useFiltroBusqueda();

onMounted(() => {
  obtenerFiltrosDisponibles();
  if (
    resultados.value.length === 0 &&
    Object.keys(filtrosSeleccionados.value).length
  ) {
    realizarBusqueda(); // recarga si no hay resultados pero sí filtros
  }
});

function agregarProveedorDesdeItem(item: Item) {
  const nombre = item.proveedorDescripcion?.trim();

  const yaExiste = store.proveedores.some(
    (p) =>
      p.companyName?.toLowerCase() === nombre?.toLowerCase()
  );

  if (yaExiste) {
    return; // no lo agregues de nuevo
  }
  const proveedor: Proveedor = {
    companyName: item.proveedorDescripcion,
    companyOwnerName: "",
    address: "",
    tipo: "existente",
    correoPersonalizado: {
      subject: `Cotización: ${store.nombre}`,
      body: `Estimado proveedor, se le ha enviado una nueva cotización.`,
      cc: [],
    },
  };
  store.agregarProveedor(proveedor);
}

function abrirSelectorParaSku(sku: Item) {
  // Agregar el item directamente a la cotización
  store.agregarItem(sku);
  alert(`Se ha agregado el item "${sku.material}" a la cotización.`);
}

// Esta función ya no es necesaria porque los items se agregan directamente a la cotización
// La mantenemos por compatibilidad pero ahora agrega el item directamente
function agregarSkuAlProveedorSeleccionado(proveedorId: string) {
  if (skuSeleccionado.value) {
    // En lugar de agregar el SKU al proveedor, lo agregamos directamente a la cotización
    store.agregarItem(skuSeleccionado.value);
    skuSeleccionado.value = null;
    mostrarSelectorProveedor.value = false;
    alert("Item agregado a la cotización.");
  }
}

function limpiarFiltros() {
  filtrosSeleccionados.value = {};
}

async function handleBuscar() {
  await realizarBusqueda(); // Lógica de búsqueda del composable
  await nextTick(); // Esperá que el DOM se actualice

  if (tablaRef.value && resultados.value.length > 0) {
    tablaRef.value.scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  }
}


</script>

<style lang="css" scoped>
/* Estilos para las secciones */
.section-container {
  animation: fadeIn 0.4s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.d-container {
  border: 1px solid #ffffffc6;
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: #e5e5e5dc;
}

.d-container:hover {
  background-color: conic-gradient(#ffe100, #ba8b00, #ffdd1c) border-box;
  box-shadow: 0 0 0 0.3rem rgba(255, 188, 34, 0.25);
}

.pi-search,
.pi-users {
  font-size: 2rem;
}

.pi-search {
  transition: all 0.3s ease;
  color: #929292;
}

.pi-search:hover {
  transform: scale(1.2);
  color: var(--secondary);
}

option {
  background-color: #fefefedc;
}

/* Estilos para los inputs y selects */
input {
  background-color: #fefefe0b;
  border-bottom: 0.2rem solid #0000005e;
}

input:focus,
select:focus {
  outline: none;
  border-color: var(--secondary);
  box-shadow: 0 0 0 0.2rem rgba(255, 188, 34, 0.25);
}

input:hover,
select:hover {
  outline: none;
  border-color: var(--secondary);
  box-shadow: 0 0 0 0.2rem rgba(255, 188, 34, 0.25);
}

/* Estilos para los filtros aplicados */
.filtros-aplicados {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filtros-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  min-height: 40px;
}

.filtro-tag {
  display: flex;
  align-items: center;
  background: #626262;
  border-radius: 10px;
  padding: 5px 12px;
  font-size: 0.9rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.filtro-tag:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.filtro-nombre {
  font-weight: 600;
  margin-right: 5px;
  color: #ebebeb;
  font-size: 1.2rem;
}

.filtro-nombre::after {
  content: ":";
}

.filtro-valor {
  color: #ebebeb;
  margin-right: 8px;
  margin-left: 8px;
  font-size: 1.2rem;
}

/* Estilos para el botón de búsqueda centralizado */
.btn-search {
  background: linear-gradient(135deg, #ffe030, #ca9d0a);
  border: none;
  border-radius: 25px;
  padding: 10px 30px;
  font-weight: 600;
  box-shadow: 0 2px 5px rgb(255, 223, 79);
  transition: all 0.3s ease;
}

.btn-search:hover:not(:disabled) {
  transform: translateY(-3px);
  scale: 1.2;
}

.btn-search:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(255, 218, 10, 0.2);
}

.btn-search:disabled {
  background: linear-gradient(135deg, #a0a0a0, #c0c0c0);
  cursor: not-allowed;
  opacity: 0.7;
}
</style>
