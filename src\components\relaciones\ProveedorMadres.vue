<template>
  <div class="relacion-container">
    <div class="relacion-header">
      <h5 class="fw-bold">
        <i class="pi pi-th-large me-2"></i> Madres relacionadas con Proveedor
      </h5>
      <div class="proveedor-info">
        <span class="badge bg-info">Proveedor: {{ mapeoBackend.proveedorDescripcion }}</span>
      </div>
    </div>

    <div class="relacion-content">
      <div v-if="cargando" class="text-center py-4">
        <div class="spinner-border text-warning" role="status">
          <span class="visually-hidden">Cargando...</span>
        </div>
        <p class="mt-2">Buscando madres relacionadas...</p>
      </div>

      <div v-else-if="madresRelacionadas.length === 0" class="text-center py-4">
        <i class="pi pi-exclamation-triangle text-warning" style="font-size: 2rem"></i>
        <p class="mt-2">No se encontraron madres relacionadas con este Proveedor.</p>
      </div>

      <div v-else class="madres-list">
        <div class="list-header">
          <div>
            <span class="text-muted">Seleccione las madres</span>
          </div>
          <span class="badge bg-primary">{{ madresRelacionadas.length }} madres</span>
        </div>

        <div class="list-body">
          <div
            v-for="(madre, index) in madresRelacionadas"
            :key="index"
            class="madre-item"
          >
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                :id="`madre-${index}`"
                v-model="madresSeleccionadas[index]"
              >
              <label class="form-check-label" :for="`madre-${index}`">
                {{ madre.madreId }} - {{ madre.madreDescripcion }}
              </label>
            </div>
            <div class="madre-badges">
              <span class="badge bg-light text-dark">ID: {{ madre.madreId }}</span>
              <span v-if="madre.sku || madre._esNuevaMadre" class="badge bg-light text-dark">SKU: {{ madre.sku }}</span>
              <span v-if="madre._esNuevaMadre" class="badge bg-success text-white">N/R</span>
              <!-- <span v-if="madre.level1" class="badge bg-info text-dark">{{ madre.level1 }}</span>
              <span v-if="madre.level2" class="badge bg-secondary text-white">{{ madre.level2 }}</span>
              <span v-if="madre.level3" class="badge bg-warning text-dark">{{ madre.level3 }}</span> -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="relacion-footer">
      <div>
        <button
            class="btn btn-success me-2"
            @click="openModal"
            title="Agregar madre no relacionada"
        >
            <i class="pi pi-plus"></i> Agregar madre no relacionada
        </button>

        <ModalMadreNoRelacionada
            :isVisible="showModal"
            :acreedor="mapeoBackend.proveedorCod"
            :nombreProveedor="mapeoBackend.proveedorDescripcion"
            :madresRelacionadas="madresRelacionadas"
            @close="closeModal"
            @madre-added="handleMadreAdded"
        />
      </div>
      <button
        class="btn btn-primary"
        @click="emitirMadresSeleccionadas"
        :disabled="!hayMadresSeleccionadas"
      >
        <i class="pi pi-check me-2"></i> Agregar seleccionadas
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import type { Item } from '@/interfaces/ItemsInterface';
import { RelacionesService } from "@/services/relacionesService";
import ModalMadreNoRelacionada from "../modals/ModalMadreNoRelacionada.vue";

const showModal = ref(false);

function openModal() {
    showModal.value = true;
}

function closeModal() {
    showModal.value = false;
}

// Definir un tipo extendido para las madres que pueden incluir propiedades adicionales
type ItemExtendido = Item & { _esNuevaMadre?: boolean };

function handleMadreAdded(madre: ItemExtendido) {
    console.log('Madre agregada:', madre);

    // Agregar la nueva madre a la lista de madres relacionadas
    if (madre._esNuevaMadre) {
        // Asegurarse de que la madre tenga la información del proveedor
        madre.proveedorCod = props.mapeoBackend.proveedorCod;
        madre.proveedorDescripcion = props.mapeoBackend.proveedorDescripcion;

        // Agregar la nueva madre al inicio de la lista para que sea más visible
        madresRelacionadas.value.unshift(madre);

        // Inicializar la selección para la nueva madre
        madresSeleccionadas.value[0] = true;

        // Actualizar el estado de "seleccionar todas"
        const values = Object.values(madresSeleccionadas.value);
        seleccionarTodasMadres.value = values.length > 0 && values.every(v => v);
    }
}

const props = defineProps<{
  mapeoBackend: Item;
}>();

const emit = defineEmits<{
  (e: 'seleccionarMadres', items: Item[]): void;
}>();

const cargando = ref(true);
const madresRelacionadas = ref<ItemExtendido[]>([]);
const madresSeleccionadas = ref<Record<number, boolean>>({});
const seleccionarTodasMadres = ref(false);

// Función para cargar madres relacionadas con el Proveedor
async function cargarMadresRelacionadas() {
  try {
    cargando.value = true;
    madresSeleccionadas.value = {}; // Resetear selecciones

    if (props.mapeoBackend && props.mapeoBackend.proveedorCod) {
      // console.log("Buscando madres para proveedor:", props.mapeoBackend.proveedorDescripcion);

      // Obtener madres relacionadas con el proveedor (creditor)
      madresRelacionadas.value = await RelacionesService.obtenerMadresRelacionadas(props.mapeoBackend.proveedorDescripcion);

      // console.log("Madres relacionadas cargadas:", madresRelacionadas.value);

      // Inicializar el objeto de selección
      madresRelacionadas.value.forEach((_, index) => {
        madresSeleccionadas.value[index] = false;
      });
    } else {
      madresRelacionadas.value = [];
    }
  } catch (error) {
    console.error("Error al cargar madres relacionadas:", error);
    madresRelacionadas.value = [];
  } finally {
    cargando.value = false;
  }
}

// Cargar datos al montar el componente
onMounted(cargarMadresRelacionadas);

// Observar cambios en las propiedades para recargar los datos
watch(
  [() => props.mapeoBackend.proveedorCod, () => props.mapeoBackend.proveedorDescripcion],
  ([nuevoAcreedor]) => {
    if (nuevoAcreedor) {
      console.log("Nuevo acreedor detectado:", nuevoAcreedor);
      cargarMadresRelacionadas();
    }
  }
);

// Computed para verificar si hay madres seleccionadas
const hayMadresSeleccionadas = computed(() => {
  return Object.values(madresSeleccionadas.value).some(selected => selected);
});

// Función para seleccionar/deseleccionar todas las madres
function toggleSeleccionarTodasMadres() {
  const nuevoEstado = seleccionarTodasMadres.value;

  madresRelacionadas.value.forEach((_, index) => {
    madresSeleccionadas.value[index] = nuevoEstado;
  });
}

// Función para emitir las madres seleccionadas
function emitirMadresSeleccionadas() {
  const seleccionadas = madresRelacionadas.value.filter((_, index) =>
    madresSeleccionadas.value[index]
  );

  // Marcar las madres nuevas para que se puedan identificar en el componente padre
  seleccionadas.forEach(madre => {
    if (madre._esNuevaMadre) {
      console.log('Emitiendo madre nueva:', madre);
    }
  });

  emit('seleccionarMadres', seleccionadas);
  console.log('Madres seleccionadas:', seleccionadas);
}

onMounted(() => {
  // console.log("Componente ProveedorMadres montado");
  // console.log("Datos iniciales:", props.mapeoBackend);
  cargarMadresRelacionadas();
});

// Observar cambios en las selecciones individuales para actualizar "seleccionar todas"
// watch(madresSeleccionadas, (newVal) => {
//   const values = Object.values(newVal);
//   seleccionarTodasMadres.value = values.length > 0 && values.every(v => v);
// }, { deep: true });

watch(
  () => props.mapeoBackend,
  (nuevoValor) => {
    console.log("Cambio en mapeoBackend:", nuevoValor);
    if (nuevoValor && nuevoValor.proveedorDescripcion) {
      cargarMadresRelacionadas();
    }
  },
  { deep: true }
);
</script>

<style scoped>
.relacion-container {
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.relacion-header {
  background-color: #e9ecef;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.proveedor-info {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.relacion-content {
  padding: 1rem;
  flex-grow: 1;
  overflow-y: auto;
  max-height: 300px;
}

.relacion-footer {
  padding: 1rem;
  background-color: #e9ecef;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: center;
}

.madres-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #e9ecef;
  border-radius: 5px;
}

.list-body {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.madre-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.madre-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.madre-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.badge {
  font-size: 0.6rem;
  padding: 0.2rem 0.2rem;
}
</style>
