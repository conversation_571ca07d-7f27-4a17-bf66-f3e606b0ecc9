<template>
    <div v-if="isVisible" class="modal-container">
        <div class="modal-content">
            <h2 class="modal-title">Agregar Madre</h2>
            <form @submit.prevent="addSelectedMadre">
                <div class="form-group">
                    <label for="madreSelect">Seleccionar Madre:</label>
                    <div class="select-container">
                        <input
                            type="text"
                            v-model="searchTerm"
                            placeholder="Buscar por ID o descripción..."
                            class="search-input"
                            @input="filterMadres"
                            :disabled="loading"
                        />

                        <!-- Mensaje de carga -->
                        <div v-if="loading" class="loading-message">
                            <div class="spinner"></div>
                            <p>Cargando madres...</p>
                        </div>

                        <!-- Mensaje cuando no hay madres -->
                        <div v-else-if="!loading && filteredMadres.length === 0" class="no-data-message">
                            <p>No se encontraron madres disponibles.</p>
                        </div>

                        <!-- Select con madres -->
                        <select
                            v-else
                            id="madreSelect"
                            v-model="selectedMadre"
                            class="form-select"
                            size="5"
                            required
                        >
                            <option
                                v-for="madre in filteredMadres"
                                :key="madre.madreId"
                                :value="madre"
                            >
                                {{ madre.madreId }} | {{ madre.madreDescripcion }} | {{ madre.sku }}
                            </option>
                        </select>
                    </div>
                </div>
                <div class="modal-buttons">
                    <button type="submit" :disabled="!selectedMadre || loading">Agregar</button>
                    <button type="button" @click="closeModal">Cancelar</button>
                </div>
            </form>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import type { Item } from '@/interfaces/ItemsInterface';
import { RelacionesService } from '@/services/relacionesService';

const props = defineProps({
    isVisible: {
        type: Boolean,
        default: false
    },
    acreedor: {
        type: String,
        default: ''
    },
    nombreProveedor: {
        type: String,
        default: ''
    },
    madresRelacionadas: {
        type: Array as () => Item[],
        default: () => []
    }
});

const emit = defineEmits(['close', 'madre-added']);

// Lista de todas las madres disponibles
const todasMadres = ref<Item[]>([]);
// Madre seleccionada del select
const selectedMadre = ref<Item | null>(null);
// Término de búsqueda para filtrar madres
const searchTerm = ref('');
// Madres filtradas según el término de búsqueda
const filteredMadres = ref<Item[]>([]);
// Estado de carga
const loading = ref(false);

// Cargar todas las madres al montar el componente
onMounted(async () => {
    await cargarMadres();
});

// Observar cambios en isVisible para recargar madres cuando se abre el modal
watch(() => props.isVisible, async (newValue) => {
    if (newValue) {
        await cargarMadres();
    }
});

// Función para cargar todas las madres
async function cargarMadres() {
    loading.value = true;
    try {
        // Obtener todas las madres
        todasMadres.value = await RelacionesService.obtenerTodasMadres();
        console.log('Todas las madres:', todasMadres.value);

        // Filtrar para excluir las madres ya relacionadas con el proveedor
        const madresRelacionadasIds = props.madresRelacionadas.map(m => m.madreId);

        todasMadres.value = todasMadres.value.filter(m =>
            !madresRelacionadasIds.includes(m.madreId)
        );

        // Inicializar las madres filtradas con todas las madres
        filteredMadres.value = [...todasMadres.value];
    } catch (error) {
        console.error('Error al cargar madres:', error);
    } finally {
        loading.value = false;
    }
}

// Función para filtrar madres según el término de búsqueda
function filterMadres() {
    if (!searchTerm.value) {
        filteredMadres.value = [...todasMadres.value];
        return;
    }

    const term = searchTerm.value.toLowerCase();
    filteredMadres.value = todasMadres.value.filter(m =>
        m.madreId?.toLowerCase().includes(term) ||
        m.madreDescripcion?.toLowerCase().includes(term)
    );
}

// Función para agregar la madre seleccionada
function addSelectedMadre() {
    if (!selectedMadre.value) return;

    // Crear una nueva madre con el formato Item
    const newMadre: Item = {
        ...selectedMadre.value,
        acreedor: props.acreedor,
        nombreProveedor: props.nombreProveedor,
        // Agregar una propiedad para identificar que es una madre nueva para este proveedor
        _esNuevaMadre: true
    } as Item & { _esNuevaMadre: boolean };

    // Emitir la nueva madre
    emit('madre-added', newMadre);

    resetForm();
    closeModal();
}

function closeModal() {
    emit('close');
}

function resetForm() {
    selectedMadre.value = null;
    searchTerm.value = '';
}
</script>

<style scoped>
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
}

.modal-title {
    margin-top: 0;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.select-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.search-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 5px;
}

.form-select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    height: auto;
    min-height: 150px;
}

.form-select option {
    padding: 8px;
    cursor: pointer;
}

.form-select option:hover {
    background-color: #f5f5f5;
}

.loading-message, .no-data-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-height: 150px;
}

.loading-message p, .no-data-message p {
    margin-top: 10px;
    color: #666;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-left-color: #4CAF50;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.modal-buttons button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.modal-buttons button[type="submit"] {
    background-color: #4CAF50;
    color: white;
}

.modal-buttons button[type="submit"]:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.modal-buttons button[type="button"] {
    background-color: #f44336;
    color: white;
}

.acreedor {
    font-size: 0.8rem;
    color: #2cdc35;
}

</style>