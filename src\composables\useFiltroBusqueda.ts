import { ref } from "vue";
import type { Item } from "@/interfaces/ItemsInterface";
import { FiltroService } from "@/services/filtrosService";
import { useCotizacionStore } from "@/stores/cotizacionStore";
import { storeToRefs } from "pinia";
import Swal from 'sweetalert2';

export function useFiltroBusqueda() {
  const tipoBusqueda = ref("");
  const filtros = ref<{ id: number; valor: string; etiqueta: string }[]>([]);
  const valorBusqueda = ref("");
  const resultados = ref<Item[]>([]);
  const sinResultados = ref(false);
  const isLoading = ref(false);
  const store = useCotizacionStore();
  const { filtrosSeleccionados } = storeToRefs(store);
  // Mapeo de campos del frontend al backend
  const mapaCamposBackend: Record<string, string> = {
    // Mapeos para madreId (ID de madre)
    madre: "madreId",
    madre_id: "madreId",
    id_madre: "madreId",

    // Mapeos directos según FilterDTO real
    material: "sku",  // Mapea "material" a "sku"
    sku: "sku",
    madreId: "madreId",
    grupo_analisis: "grupoAnalisisDescripcion",
    proveedor_codigo: "proveedorCod",
    proveedor_nombre: "proveedorDescripcion",
    nombre_proveedor: "proveedorDescripcion",
    feria: "feriaId",
    feria_id: "feriaId",

    nivel_1: "n1Descripcion",
    nivel_2: "n2Descripcion",
    nivel_3: "n3Descripcion",
    nivel_4: "n4Descripcion",

    // Otros campos
    descripcion_madre: "madreDescripcion", // Corregido: motherDescription -> madreDescripcion
    madre_descripcion: "madreDescripcion", // Corregido: motherDescription -> madreDescripcion
    madreDescripcion: "madreDescripcion", // Corregido: motherDescription -> madreDescripcion

    // Campos adicionales que podrían ser útiles
    proveedor: "proveedorDescripcion",
    proveedorCod: "proveedorCod",

    // Campos originales que podrían seguir siendo válidos
    fob: "fob",
    cantidadPedido: "moq",
    precioNetoPedido: "fob",
    temporada: "feriaId",
    textoTemporada: "temporalidad",
    acreedor: "proveedorCod",
  };

  //Flush
  async function obtenerFiltrosDisponibles() {
    try {
      filtros.value = await FiltroService.obtenerFiltros(); // asumimos que es global o importado
    } catch (err) {
      // console.error("❌ Error al obtener filtros:", err);
    }
  }

  function agregarFiltro() {
    // console.log('=== DEBUG: Inicio agregarFiltro ===');
    // console.log('tipoBusqueda:', tipoBusqueda.value);
    // console.log('valorBusqueda:', valorBusqueda.value);

    // Validar que se haya seleccionado un tipo de búsqueda y se haya ingresado un valor
    if (!tipoBusqueda.value || !valorBusqueda.value.trim()) {
      // console.warn("Debe seleccionar un tipo de búsqueda e ingresar un valor");
      return;
    }

    // console.log('Campo antes del mapeo:', tipoBusqueda.value);
    const campoReal = mapaCamposBackend[tipoBusqueda.value];
    // console.log('Campo después del mapeo:', campoReal);

    if (!campoReal) {
      // console.warn("Campo no reconocido por el backend:", tipoBusqueda.value);
      // console.log('Mapeo disponible:', mapaCamposBackend);
      return;
    }

    // Verificar si parece ser una descripción en lugar de un ID
    if (campoReal === 'madreId' && valorBusqueda.value.length > 10 && !/^\d+$/.test(valorBusqueda.value)) {
      // Si parece una descripción (texto largo sin solo dígitos), usar madreDescripcion
      filtrosSeleccionados.value['madreDescripcion'] = valorBusqueda.value.toUpperCase();
    } else {
      // Caso normal
      filtrosSeleccionados.value[campoReal] = valorBusqueda.value.toUpperCase();
    }

    // console.log('Filtros actuales:', JSON.stringify(filtrosSeleccionados.value, null, 2));

    // Limpiar los campos después de agregar el filtro
    tipoBusqueda.value = "";
    valorBusqueda.value = "";

    // console.log('=== DEBUG: Fin agregarFiltro ===');
  }

  function obtenerEtiquetaFiltro(campo: string): string {
    const mapeoInverso: Record<string, string> = {
      // Campos según FilterDTO real
      sku: "SKU",
      madreId: "ID Madre",
      grupoAnalisisDescripcion: "Grupo de Análisis",
      proveedorCod: "Código Proveedor",
      proveedorDescripcion: "Nombre Proveedor",
      feriaId: "Feria",
      n1Descripcion: "Nivel 1",
      n2Descripcion: "Nivel 2",
      n3Descripcion: "Nivel 3",
      n4Descripcion: "Nivel 4",
      madreDescripcion: "Descripción Madre", // Corregido: motherDescription -> madreDescripcion
    };
    return mapeoInverso[campo] || campo;
    console.log('Etiqueta del filtro:', mapeoInverso[campo] || campo);  
  }

  async function realizarBusqueda() {
    // console.log('=== DEBUG: Inicio realizarBusqueda ===');

    // Añadir estado de carga
    isLoading.value = true;
    sinResultados.value = false;

    // Protección extra por si algo raro pasa
    if (
      !filtrosSeleccionados.value ||
      Object.keys(filtrosSeleccionados.value).length === 0
    ) {
      // console.log('No se logró hacer la búsqueda: no hay filtros seleccionados');
      isLoading.value = false;
      return;
    }

    try {
      // console.log("Buscando con filtros:", JSON.stringify(filtrosSeleccionados.value, null, 2));

      // Corregir los filtros antes de enviarlos
      const filtrosCorregidos = FiltroService.corregirFiltros(filtrosSeleccionados.value);
      // console.log("Filtros corregidos:", JSON.stringify(filtrosCorregidos, null, 2));
      
      const response = await FiltroService.realizarBusqueda(
        filtrosCorregidos // Usar los filtros corregidos
      );

      // Cerrar mensaje de carga
      Swal.close();

      // Verificar si la respuesta tiene datos
      if (response && response.data) {
        // console.log("Respuesta completa:", response);
        // console.log("Datos recibidos:", response.data);

        // Verificar si los datos son un array
        if (Array.isArray(response.data)) {
          // Asignar los datos a resultados
          resultados.value = response.data;

          // Verificar si hay resultados
          sinResultados.value = response.data.length === 0;

          // console.log("Resultados procesados (array):", resultados.value.length);
          // console.log("¿Sin resultados?", sinResultados.value);
        } else if (typeof response.data === 'object') {
          // Si no es un array pero es un objeto, podría ser que los datos estén en una propiedad
          // console.warn("La respuesta no es un array, buscando datos en el objeto...");

          // Buscar una propiedad que contenga un array
          const arrayProperty = Object.keys(response.data).find(key => {
            // Verificar si la propiedad existe y es un array
            const value = response.data[key as keyof object];
            return Array.isArray(value);
          });

          if (arrayProperty) {
            // console.log(`Encontrados datos en response.data.${arrayProperty}`);
            resultados.value = response.data[arrayProperty];
            sinResultados.value = resultados.value.length === 0;
          } else {
            // Si no encontramos un array, intentar convertir el objeto a un array
            // console.warn("No se encontró un array en la respuesta, intentando convertir el objeto...");

            // Verificar si el objeto tiene propiedades que coinciden con los campos esperados
            const expectedFields = ['sku', 'madreId', 'madreDescripcion', 'proveedorCod', 'proveedorDescripcion'];
            const hasExpectedFields = expectedFields.some(field => field in response.data);

            if (hasExpectedFields) {
              // Si el objeto tiene los campos esperados, podría ser un solo resultado
              // console.log("El objeto parece ser un solo resultado, convirtiéndolo a array");
              resultados.value = [response.data];
              sinResultados.value = false;
            } else {
              // console.error("No se pudo encontrar datos válidos en la respuesta");
              resultados.value = [];
              sinResultados.value = true;
            }
          }
        } else {
          // console.warn("La respuesta no contiene datos válidos:", response.data);
          resultados.value = [];
          sinResultados.value = true;
        }

        sinResultados.value = resultados.value.length === 0;

        // console.log(`Búsqueda completada: ${resultados.value.length} resultados encontrados`);

        if (resultados.value.length === 0) {
          // console.warn("No se encontraron resultados para los filtros aplicados");

          // Mostrar mensaje al usuario
          Swal.fire({
            title: 'Sin resultados',
            text: 'No se encontraron resultados para los filtros seleccionados. Intente con otros criterios de búsqueda.',
            icon: 'info',
            confirmButtonText: 'Entendido'
          });
        } else {
          // console.log("Muestra de resultados:", resultados.value.slice(0, 2));

          // Mostrar mensaje de éxito
          Swal.fire({
            title: 'Búsqueda completada',
            text: `Se encontraron ${resultados.value.length} resultados`,
            icon: 'success',
            timer: 1500,
            showConfirmButton: false
          });
        }
      } else {
        // console.warn("La respuesta no contiene datos:", response);
        resultados.value = [];
        sinResultados.value = true;

        // Mostrar mensaje al usuario
        Swal.fire({
          title: 'Sin resultados',
          text: 'La respuesta del servidor no contiene datos. Intente con otros criterios de búsqueda.',
          icon: 'info',
          confirmButtonText: 'Entendido'
        });
      }
    } catch (error) {
      // console.error("Error en búsqueda:", error);
      resultados.value = [];
      sinResultados.value = true;

      // Mostrar mensaje de error al usuario
      Swal.fire({
        title: 'Error en la búsqueda',
        text: 'Ocurrió un error al realizar la búsqueda. Por favor, intente nuevamente.',
        icon: 'error',
        confirmButtonText: 'Entendido'
      });
    } finally {
      isLoading.value = false;
      // console.log('=== DEBUG: Fin realizarBusqueda ===');
    }
  }

  function limpiarResultados() {
    resultados.value = [];
    sinResultados.value = false;
  }

  return {
    tipoBusqueda,
    filtros,
    valorBusqueda,
    filtrosSeleccionados,
    resultados,
    sinResultados,
    isLoading,
    agregarFiltro,
    obtenerEtiquetaFiltro,
    realizarBusqueda,
    limpiarResultados,
    obtenerFiltrosDisponibles,
  };
}
