// src/stores/homeStore.ts
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useHomeStore = defineStore('home', () => {
  const loading = ref(false)
  const error = ref<string | null>(null)

  const proveedores = ref(0)
  const proveedoresNuevos = ref(0)
  const cotizacionesPendientes = ref(0)
  const seguimientos = ref(0)
  const proveedoresRespondidos = ref(0)
  const logs = ref(0)

  async function fetchDashboard() {
    loading.value = true
    error.value = null

    try {
      // Aquí iría tu llamada real al backend:
      // const response = await getDashboardData()
      // Simulamos datos mock
      const response = {
        proveedores: 25,
        proveedoresNuevos: 3,
        cotizacionesPendientes: 10,
        seguimientos: 5,
        proveedoresRespondidos: 2,
        logs: 50,
      }

      proveedores.value = response.proveedores
      proveedoresNuevos.value = response.proveedoresNuevos
      cotizacionesPendientes.value = response.cotizacionesPendientes
      seguimientos.value = response.seguimientos
      proveedoresRespondidos.value = response.proveedoresRespondidos
      logs.value = response.logs
    } catch (err) {
      error.value = 'No se pudo cargar el dashboard'
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    proveedores,
    proveedoresNuevos,
    cotizacionesPendientes,
    seguimientos,
    proveedoresRespondidos,
    logs,
    fetchDashboard,
  }
})