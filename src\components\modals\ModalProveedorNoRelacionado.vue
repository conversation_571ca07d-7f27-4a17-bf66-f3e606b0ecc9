<template>
    <div v-if="isVisible" class="modal-container">
        <div class="modal-content">
            <h2 class="modal-title">Agregar Proveedor</h2>
            <form @submit.prevent="addSelectedSupplier">
                <div class="form-group">
                    <label for="proveedorSelect">Seleccionar Proveedor:</label>
                    <div class="select-container">
                        <input
                            type="text"
                            v-model="searchTerm"
                            placeholder="Buscar por nombre o acreedor..."
                            class="search-input"
                            @input="filterProveedores"
                            :disabled="loading"
                        />

                        <!-- Mensaje de carga -->
                        <div v-if="loading" class="loading-message">
                            <div class="spinner"></div>
                            <p>Cargando proveedores...</p>
                        </div>

                        <!-- Men<PERSON>je cuando no hay proveedores -->
                        <div v-else-if="!loading && filteredProveedores.length === 0" class="no-data-message">
                            <p>No se encontraron proveedores disponibles.</p>
                        </div>

                        <!-- Select con proveedores -->
                         
                        <select
                            v-else
                            id="proveedorSelect"
                            v-model="selectedProveedor"
                            class="form-select"
                            size="5"
                            required
                        >
                            <option
                                v-for="proveedor in filteredProveedores"
                                :key="proveedor.proveedorCod"
                                :value="proveedor"
                            >

                                creditor: {{ proveedor.proveedorCod }} | {{ proveedor.proveedorDescripcion }} 
                                                                

                        </option>
                        </select>
                        
                    </div>
                    
                </div>
                <div class="modal-buttons">
                    <button type="submit" :disabled="!selectedProveedor || loading">Agregar</button>
                    <button type="button" @click="closeModal">Cancelar</button>
                </div>
            </form>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import type { Item } from '@/interfaces/ItemsInterface';
import { RelacionesService } from '@/services/relacionesService';

const props = defineProps({
    isVisible: {
        type: Boolean,
        default: false
    },
    madreId: {
        type: String,
        default: ''
    },
    descripcionMadre: {
        type: String,
        default: ''
    },
    proveedoresRelacionados: {
        type: Array as () => Item[],
        default: () => []
    }
});

const emit = defineEmits(['close', 'supplier-added']);

// Lista de todos los proveedores disponibles
const todosProveedores = ref<Item[]>([]);
// Proveedor seleccionado del select
const selectedProveedor = ref<Item | null>(null);
// Término de búsqueda para filtrar proveedores
const searchTerm = ref('');
// Proveedores filtrados según el término de búsqueda
const filteredProveedores = ref<Item[]>([]);
// Estado de carga
const loading = ref(false);

// Cargar todos los proveedores al montar el componente
onMounted(async () => {
    await cargarProveedores();
});

// Observar cambios en isVisible para recargar proveedores cuando se abre el modal
watch(() => props.isVisible, async (newValue) => {
    if (newValue) {
        await cargarProveedores();
    }
});

// Función para cargar todos los proveedores
async function cargarProveedores() {
    loading.value = true;
    try {
        // Obtener todos los proveedores
        todosProveedores.value = await RelacionesService.obtenerTodosProveedores();
        // console.log('Todos los proveedores:', todosProveedores.value);

        // Filtrar para excluir los proveedores ya relacionados con la madre
        const proveedoresRelacionadosIds = props.proveedoresRelacionados.map(p => p.proveedorCod);

        todosProveedores.value = todosProveedores.value.filter(p =>
            !proveedoresRelacionadosIds.includes(p.proveedorCod)
        );

        // Inicializar los proveedores filtrados con todos los proveedores
        filteredProveedores.value = [...todosProveedores.value];
    } catch (error) {
        console.error('Error al cargar proveedores:', error);
    } finally {
        loading.value = false;
    }
}

// Función para filtrar proveedores según el término de búsqueda
function filterProveedores() {
    if (!searchTerm.value) {
        filteredProveedores.value = [...todosProveedores.value];
        return;
    }

    const term = searchTerm.value.toLowerCase();
    filteredProveedores.value = todosProveedores.value.filter(p =>
        p.proveedorDescripcion?.toLowerCase().includes(term) ||
        p.proveedorCod?.toLowerCase().includes(term)
    );
}

// Función para agregar el proveedor seleccionado
function addSelectedSupplier() {
    if (!selectedProveedor.value) return;

    // Crear un nuevo proveedor con el formato Item
    const newSupplier: Item = {
        ...selectedProveedor.value,
        madreId: props.madreId,
        descripcionMadre: props.descripcionMadre,
        // Agregar una propiedad para identificar que es un proveedor nuevo para esta madre
        _esNuevoProveedor: true
    } as Item & { _esNuevoProveedor: boolean };

    // Emitir el nuevo proveedor
    emit('supplier-added', newSupplier);

    resetForm();
    closeModal();
}

function closeModal() {
    emit('close');
}

function resetForm() {
    selectedProveedor.value = null;
    searchTerm.value = '';
}
</script>

<style scoped>
.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
}

.modal-title {
    margin-top: 0;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.select-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.search-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 5px;
}

.form-select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    height: auto;
    min-height: 150px;
}

.form-select option {
    padding: 8px;
    cursor: pointer;
}

.form-select option:hover {
    background-color: #f5f5f5;
}

.loading-message, .no-data-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-height: 150px;
}

.loading-message p, .no-data-message p {
    margin-top: 10px;
    color: #666;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-left-color: #4CAF50;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.modal-buttons button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.modal-buttons button[type="submit"] {
    background-color: #4CAF50;
    color: white;
}

.modal-buttons button[type="submit"]:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.modal-buttons button[type="button"] {
    background-color: #f44336;
    color: white;
}

.acreedor {
    font-size: 0.8rem;
    color: #2cdc35;
}

</style>