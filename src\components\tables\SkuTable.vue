<template>
  <div class="table-container">
    <DataTable
      :value="resultados"
      v-model:selection="selectedItem"
      resizableColumns
      columnResizeMode="expand"
      showGridlines
      filterDisplay="menu"
      scrollable
      scrollDirection="horizontal"
      scrollHeight="290px"
      :paginator="true"
      :rows="10"
      :rowsPerPageOptions="[10, 20, 50]"
      class="Datatable"
      dataKey="sku"
      @row-select="onRowSelect"
      @row-unselect="onRowUnselect"
      selectionMode="single"
      :loading="false" 
      emptyMessage="No hay resultados"
    >
      <!-- <Column header="Imagen" class="text-center" sortable>
        <template #body="slotProps">
          <div class="image-container">
            <img
              v-if="slotProps.data.imagen"
              :src="slotProps.data.imagen"
              class="sku-image"
              @click="openImageModal(slotProps.data.imagen)"
            />
            <div v-else class="no-image">Sin imagen</div>
          </div>
        </template>
      </Column> -->
      <Column selectionMode="single" headerStyle="width: 3rem" :exportable="false">
      </Column>

      <Column field="madreId" header="Madre" class="text-center" sortable />

      <Column
      field="madreDescripcion"
      header="Descripción Madre"
      class="text-center"
      sortable
      />
      <Column
      field="proveedorDescripcion"
      header="Nombre Proveedor"
      class="text-center"
      sortable
      />

      <Column field="proveedorCod" header="Acreedor" class="text-center" sortable />

      <Column field="sku" header="SKU" class="text-center" sortable/>
      <Column field="feriaId" header="Feria" class="text-center" sortable />
      <Column
        field="grupoAnalisisDescripcion"
        header="Grupo de Análisis"
        class="text-center"
        sortable
      />

      <Column field="n1Descripcion" header="Nivel 1" class="text-center" sortable />
      <Column field="n2Descripcion" header="Nivel 2" class="text-center" sortable />
      <Column field="n3Descripcion" header="Nivel 3" class="text-center" sortable />
      <Column field="n4Descripcion" header="Nivel 4" class="text-center" sortable />
      <Column field="moq" header="MOQ" class="text-center" sortable />
      <Column field="fob" header="FOB" class="text-center" sortable />
    </DataTable>

    <div class="button-container mt-3 d-flex justify-content-center">
      <button
        class="btn btn-primary select-btn"
        @click="handleSeleccion"
        :disabled="!selectedItem"
      >
        <i class="pi pi-check-circle me-1"></i> Seleccionar
      </button>
    </div>
  </div>

</template>



<script setup lang="ts">
import type { Item } from "@/interfaces/ItemsInterface";
import DataTable from "primevue/datatable";
import Column from "primevue/column";
import { ref } from 'vue';

const props = defineProps<{
  resultados: Item[];
}>();

// Log para depurar los datos recibidos
// console.log('Número de resultados:', props.resultados.length);
if (props.resultados.length > 0) {
  //console.log('Primer resultado:', props.resultados[0]);
  // console.log('Propiedades del primer resultado:', Object.keys(props.resultados[0]));
}

const emit = defineEmits<{
  (e: "seleccionar", items: any): void;
}>();

// Variable para almacenar el elemento seleccionado (un solo objeto, no un array)
const selectedItem = ref<Item | null>(null);

// Función para manejar la selección y emitir el evento con slotProps.data
function handleSeleccion() {
  if (selectedItem.value) {
    // console.log('Propiedades disponibles:', Object.keys(selectedItem.value));
    
    // Crear un objeto simplificado con solo las propiedades que sabemos que existen
    const propiedadesNecesarias = {
      sku: selectedItem.value.sku || 0,
      material: selectedItem.value.sku || 0,
      cantidadPedido: 0,
      precioNetoPedido: 0,
      fob: 0,
      level1: '',
      level2: '',
      level3: '',
      level4: '',
      temporada: '',
      textoTemporada: '',
      grupoAnalisis: '',
      acreedor: '',
      nombreProveedor: '',
      madreId: '',
      madreDescripcion: '',
      feriaId: '',
      imagen: ''
    };
    
    // Asignar valores solo si existen en selectedItem.value
    if ((selectedItem.value as any).moq) propiedadesNecesarias.cantidadPedido = Number((selectedItem.value as any).moq);
    if ((selectedItem.value as any).fob) {
      propiedadesNecesarias.precioNetoPedido = Number((selectedItem.value as any).fob);
      propiedadesNecesarias.fob = Number((selectedItem.value as any).fob);
    }
    if ((selectedItem.value as any).n1Descripcion) propiedadesNecesarias.level1 = String((selectedItem.value as any).n1Descripcion);
    if ((selectedItem.value as any).n2Descripcion) propiedadesNecesarias.level2 = String((selectedItem.value as any).n2Descripcion);
    if ((selectedItem.value as any).n3Descripcion) propiedadesNecesarias.level3 = String((selectedItem.value as any).n3Descripcion);
    if ((selectedItem.value as any).n4Descripcion) propiedadesNecesarias.level4 = String((selectedItem.value as any).n4Descripcion);
    if ((selectedItem.value as any).feriaId) {
      propiedadesNecesarias.temporada = String((selectedItem.value as any).feriaId);
      propiedadesNecesarias.feriaId = String((selectedItem.value as any).feriaId);
    }
    if ((selectedItem.value as any).temporalidad) propiedadesNecesarias.textoTemporada = String((selectedItem.value as any).temporalidad);
    if ((selectedItem.value as any).grupoAnalisisDescripcion) propiedadesNecesarias.grupoAnalisis = String((selectedItem.value as any).grupoAnalisisDescripcion);
    if ((selectedItem.value as any).proveedorCod) propiedadesNecesarias.acreedor = String((selectedItem.value as any).proveedorCod);
    if ((selectedItem.value as any).proveedorDescripcion) propiedadesNecesarias.nombreProveedor = String((selectedItem.value as any).proveedorDescripcion);
    if ((selectedItem.value as any).madreId) propiedadesNecesarias.madreId = String((selectedItem.value as any).madreId);
    if ((selectedItem.value as any).madreDescripcion) propiedadesNecesarias.madreDescripcion = String((selectedItem.value as any).madreDescripcion);
    if ((selectedItem.value as any).imagen) propiedadesNecesarias.imagen = (selectedItem.value as any).imagen;
    
    emit('seleccionar', [propiedadesNecesarias]);
  }
}

// Funciones para depurar la selección (modificadas para ser más concisas)
function onRowSelect(event: any) {
  // console.log('Fila seleccionada:', event.data.sku);
  
  // Mostrar solo las propiedades más relevantes
  const propiedadesRelevantes = {
    sku: event.data.sku,
    descripcion: event.data.descripcion,
    madreId: event.data.madreId,
    proveedor: event.data.proveedorDescripcion,
    fob: event.data.fob,
    material: event.data.sku,
    moq: event.data.moq,
    nivel1: event.data.n1Descripcion,
    nivel2: event.data.n2Descripcion,
    nivel3: event.data.n3Descripcion,
    nivel4: event.data.n4Descripcion,
    temporada: event.data.feriaId,
    textoTemporada: event.data.temporalidad,
    grupoAnalisis: event.data.grupoAnalisisDescripcion,
    acreedor: event.data.proveedorCod,
    nombreProveedor: event.data.proveedorDescripcion,


  };
  
  // console.log('Propiedades relevantes:', propiedadesRelevantes);
}

function onRowUnselect(event: any) {
  // console.log('Fila deseleccionada:', event.data.sku);
}
</script>
<style scoped>
.Datatable {
  background-color: #ffffff52;
  border-radius: 0.5rem;
  padding: 1rem;
}

.select-btn {
  transition: all 0.3s ease;
  min-width: 120px;
  background: linear-gradient(135deg, #4a90e2, #2c5282);
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.select-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #5a9ff2, #3c6292);
}

.button-container {
  margin-top: 1rem;
}
</style>
