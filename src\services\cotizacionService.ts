
import type { Cotizac<PERSON> } from '@/interfaces/CotizacionInterface'

export async function enviarCotizacion(payload: Cotizacion) {
  try {
    // Transformar los datos al formato esperado por el backend
    const backendPayload = {
      quotation: {  
        quotationDate: payload.fechaCreacion,
        statusId: payload.statusId,
        items: payload.items.map(item => ({
          sku: item.sku.toString(),
          name: item.madreDescripcion,
          productImage: item.imagen,
          productDescription: item.grupoAnalisisDescripcion,
          supplierItemNumber: item.madreId
        })),
        suppliers: payload.suppliers.map(supplier => ({
          supplierId: supplier.supplierId
        }))
      }


    };

    console.log('Enviando cotización al backend:', backendPayload);
    const response = await fetch('http://localhost:8080/api/quotation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(backendPayload),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Error al enviar la cotización');
    }

    return await response.json();
  } catch (error) {
    console.error('Error en enviarCotizacion:', error);
    throw error;
  }
}

export async function descargaExcelCotización(payload: Cotizacion) {
  try {
    const backendPayload = {
        id: payload.id,
        nombre: payload.nombre,
        estado: payload.estado,
        tipo: payload.tipo,
        fechaCreacion: payload.fechaCreacion,
        modificada: payload.modificada,
        items:{
          sku: payload.items.map(item => item.sku),
          madreId: payload.items.map(item => item.madreDescripcion).toString(),
          grupoAnalisisDescripcion: payload.items.map(item => item.grupoAnalisisDescripcion).toString(),
          madreDescripcion: payload.items.map(item => item.madreDescripcion).toString(),
        },
        proveedor: {
          supplierId: payload.proveedores.map(proveedor => proveedor.supplierId),
          companyName: payload.proveedores.map(proveedor => proveedor.companyName).toString(),
          email: payload.proveedores.map(proveedor => proveedor.email).toString()
        }
    }

    const response = await fetch('http://localhost:8080/cotizaciones/descargar-excel', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(backendPayload),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Error al descargar el Excel');
    }

    return await response.json();


    // Lógica para descargar el Excel
  } catch (error) {
    console.error('Error en descargaExcelCotización:', error);
    throw error;
  }
}


// import type { Cotizacion } from '@/interfaces/CotizacionInterface'

// export async function enviarCotizacion(payload: Cotizacion) {
//   try {
//     const response = await fetch('/api/cotizaciones', {
//       method: 'POST',
//       headers: { 'Content-Type': 'application/json' },
//       body: JSON.stringify(payload),
//     })
//     if (!response.ok) {
//       throw new Error('Error al enviar la cotización')
//     }

//     return await response.json()
//   } catch (error) {
//     console.error(error)
//     throw error
//   }
// }