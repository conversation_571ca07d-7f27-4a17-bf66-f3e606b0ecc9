<template>
  <div class="p-4">
    <h2>Supplier Service Test</h2>

    <div class="mb-4">
      <h4>Test by ID</h4>
      <div class="flex align-items-center mb-2">
        <label for="supplierId">Supplier ID:</label>
        <InputText
          id="supplierId"
          v-model="testId"
          placeholder="Enter supplier ID to test"
          class="ml-2"
        />
        <Button
          @click="testGetSupplierById"
          label="Test Get by ID"
          class="ml-2"
          :loading="loading"
        />
      </div>
    </div>

    <div class="mb-4">
      <h4>Test by Company Name</h4>
      <div class="flex align-items-center mb-2">
        <label for="companyName">Company Name:</label>
        <InputText
          id="companyName"
          v-model="testname"
          placeholder="Enter company name to test"
          class="ml-2"
        />
        <Button
          @click="testGetSupplier"
          label="Test Get by Company Name"
          class="ml-2"
          :loading="loading"
        />
      </div>
    </div>

    <div class="mb-4">
      <Button
        @click="testGetAllSuppliers"
        label="Test Get All Suppliers"
        :loading="loading"
        severity="secondary"
      />
    </div>

    <Card v-if="supplier">
      <template #title>Supplier Details</template>
      <template #content>
        <div class="p-fluid grid">
          <div class="field col-12 md:col-6">
            <label>Supplier ID</label>
            <InputText :value="supplier.supplier_id?.toString()" readonly />
          </div>
          <div class="field col-12 md:col-6">
            <label>Company Name</label>
            <InputText v-model="supplier.companyName" readonly />
          </div>
          <div class="field col-12 md:col-6">
            <label>Email</label>
            <InputText v-model="supplier.email" readonly />
          </div>
        </div>
      </template>
    </Card>

    <div v-if="loading" class="text-center">
      <ProgressSpinner />
    </div>

    <Message v-if="error" severity="error">{{ error }}</Message>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { SupplierService } from '@/services/newSupplierService';
import { useRoute } from 'vue-router';
import Card from 'primevue/card';
import InputText from 'primevue/inputtext';
import ProgressSpinner from 'primevue/progressspinner';
import Message from 'primevue/message';
import Button from 'primevue/button';

export interface SupplierInformationForm{
    supplier_id: number,
    companyName: string,
    companyOwnerName: string,
    companyOwnerPhone: string,
    companyOwnerEmail: string,
    socialCreditCode: string,
    exportLicenseCode: string,
    address: string,
    province: string,
    city: string,
    state: string,
    country_id: number,
    contactName: string,
    status_id: number,
    nivel3: number,
    phone: string,
    email: string,
    currencyId: number,
    bankName: string,
    bankAddress: string,
    swiftCode: string,
    ibanNumber: string,
    accountNumber: string,
    beneficiaryName: string,
    beneficiaryAddress: string,
    companyProfileStatus: string,
    annualFacturation: number,
}

const route = useRoute();
const supplier = ref<SupplierInformationForm | null>(null);
const loading = ref(false);
const error = ref('');
const testname = ref(''); // Default company name
const testId = ref('1'); // Default test ID

// Function to test the supplier service
async function testGetSupplier() {
  const companyName = String(testname.value);
  if (!companyName || companyName.trim() === '') {
    error.value = 'Please enter a company name';
    return;
  }

  try {
    loading.value = true;
    error.value = '';
    supplier.value = await SupplierService.getSupplierByCompanyName(companyName);
    console.log('Supplier data received:', supplier.value);
  } catch (err) {
    error.value = 'Failed to load supplier details';
    console.error('Error:', err);
  } finally {
    loading.value = false;
  }
}

// Function to test get supplier by ID
async function testGetSupplierById() {
  const id = Number(testId.value);
  if (isNaN(id)) {
    error.value = 'Please enter a valid supplier ID';
    return;
  }

  try {
    loading.value = true;
    error.value = '';
    supplier.value = await SupplierService.getSupplierById(id);
    console.log('Supplier data received:', supplier.value);
  } catch (err) {
    error.value = 'Failed to load supplier details';
    console.error('Error:', err);
  } finally {
    loading.value = false;
  }
}

// Function to test get all suppliers
async function testGetAllSuppliers() {
  try {
    loading.value = true;
    error.value = '';
    const suppliers = await SupplierService.getAllSuppliers();
    console.log('All suppliers data received:', suppliers);

    // For display purposes, show the first supplier if available
    if (suppliers && suppliers.length > 0) {
      supplier.value = suppliers[0];
    } else {
      error.value = 'No suppliers found';
    }
  } catch (err) {
    error.value = 'Failed to load suppliers';
    console.error('Error:', err);
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  // Check if there's a company name in the route params
  const routeCompanyName = route.params.companyName;
  if (routeCompanyName) {
    testname.value = routeCompanyName.toString();
    await testGetSupplier();
  }
});
</script>

<style scoped>
.p-4 {
  padding: 2rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

h2 {
  color: #333;
  margin-bottom: 1rem;
}

label {
  font-weight: 600;
  color: #555;
}

.field {
  margin-bottom: 1rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
}

.flex {
  display: flex;
}

.align-items-center {
  align-items: center;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

h4 {
  color: #4a5568;
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
}
</style>