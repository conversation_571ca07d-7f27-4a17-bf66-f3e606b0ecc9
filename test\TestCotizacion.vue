<template>
  <div>
    <Card v-if="supplier">
      <template #title>Supplier Details</template>
      <template #content>
        <div class="p-fluid grid">
          <div class="field col-12 md:col-6">
            <label>ID</label>
            <InputText v-model="supplier.id" readonly />
          </div>
          <div class="field col-12 md:col-6">
            <label>Name</label>
            <InputText v-model="supplier.name" readonly />
          </div>
          <!-- Add other fields as needed -->
        </div>
      </template>
    </Card>
    
    <div v-if="loading" class="text-center">
      <ProgressSpinner />
    </div>
    
    <Message v-if="error" severity="error">{{ error }}</Message>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { SupplierService } from '@/services/newSupplierService';
import { useRoute } from 'vue-router';
import { Card } from 'primevue';
import { InputText } from 'primevue';
import { ProgressSpinner } from 'primevue';
import { Message } from 'primevue/message';

export interface SupplierInformationForm{
    supplier_id: number,
    companyName: string,
    companyOwnerName: string,
    companyOwnerPhone: string,
    companyOwnerEmail: string,
    socialCreditCode: string,
    exportLicenseCode: string,
    address: string,
    province: string,
    city: string,
    state: string,
    country_id: number,
    contactName: string,
    status_id: number,
    nivel3: number,
    phone: string,
    email: string,
    currencyId: number,
    bankName: string,
    bankAddress: string,
    swiftCode: string,
    ibanNumber: string,
    accountNumber: string,
    beneficiaryName: string,
    beneficiaryAddress: string,
    companyProfileStatus: string,
    annualFacturation: number,
}

const route = useRoute();
const supplier = ref<SupplierInformationForm | null>(null);
const loading = ref(false);
const error = ref('');

onMounted(async () => {
  const id = Number(route.params.id);
  if (isNaN(id)) {
    error.value = 'Invalid supplier ID';
    return;
  }

  try {
    loading.value = true;
    supplier.value = await SupplierService.getSupplierById(id);
  } catch (err) {
    error.value = 'Failed to load supplier details';
    console.error(err);
  } finally {
    loading.value = false;
  }
});
</script>