import type { newSupplierInterface } from "@/interfaces/newSupplierInterface";
import { ref } from "vue";

export function useNewSupplier() {
    const newSuppliers = ref<newSupplierInterface[]>([]);

    function addNewSupplier(email: string, nombreProveedor?: string, acreedor?: string, madreId?: string, descripcionMadre?: string) {
        newSuppliers.value.push({
            email: email,
            nombreProveedor,
            acreedor,
            madreId,
            descripcionMadre
        });
        console.log('Nuevo proveedor agregado:', email, nombreProveedor);
    }

    function getNewSuppliers() {
        return newSuppliers.value;
    }

    return {
        newSuppliers,
        addNewSupplier,
        getNewSuppliers
    };
}