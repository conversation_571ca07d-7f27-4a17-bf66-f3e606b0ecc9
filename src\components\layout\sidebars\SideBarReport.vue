<template>
  <div>
    <ModalAllSuppliers
      v-if="visModalSuppliers"
      @click.self="closeModalSuppliers"
      @closeSuppliers="closeModalSuppliers">
    </ModalAllSuppliers>

    <ModalAllQuotes
      v-if="visModalQuotes"
      @closeQuotes="closeModalQuotes"
      @click.self="closeModalQuotes"></ModalAllQuotes>

    <div
      class="side-bar animate-fade-left animate-once animate-duration-[1000ms]">
      <div class="grid-reports">
        <h1><i class="bi bi-clipboard-data-fill title"></i> REPORT'S</h1>
      </div>

      <button @click="visModalSuppliers = true" class="btn grid-btn-suppliers">
        <h3 class="text"><i class="bi bi-person-fill"></i> Supplier's</h3>
      </button>

      <button @click="visModalQuotes = true" class="btn grid-btn-quotes">
        <h3 class="text"><i class="bi bi-clipboard-fill"></i> Quotes</h3>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import ModalAllSuppliers from "@/components/modals/ModalAllSuppliers.vue";
import ModalAllQuotes from "@/components/modals/ModalAllQuotes.vue";

const visModalSuppliers = ref(false);
const visModalQuotes = ref(false);

const closeModalSuppliers = () => {
  visModalSuppliers.value = false;
};

const closeModalQuotes = () => {
  visModalQuotes.value = false;
};
</script>

<style scoped>
.side-bar {
  border-radius: 5px;
  display: grid;
  gap: 10px;
  justify-self: flex-end;
  grid-template-columns: 250px;
  grid-template-rows: 66px 10px 66px 66px;
  background-color: var(--dark);
  max-width: 250px;

  .btn {
    .text {
      font-family: "Fira sans", sans-serif;

      color: black;
    }

    &:hover {
      .text {
        color: whitesmoke;
      }
    }
  }
}

.btn {
  text-align: center;
  border-radius: 5px;
  min-width: 250px;
  max-width: 300px;
  background-color: #ffbd22;
}

.btn:hover {
  background-color: var(--secondary);
}

.grid-reports {
  grid-row-start: 1;
  grid-row-end: 2;
  padding: 10px;
  background-color: #ffa504;
  border-radius: 5px;
}

.grid-btn-suppliers {
  display: grid;
  grid-row-start: 3;
  grid-row-end: 4;
  padding-top: 15px;
}

.grid-btn-quotes {
  display: grid;
  grid-row-start: 4;
  grid-row-end: 5;
  padding-top: 15px;
}

.title {
  justify-items: center;
  font-family: "Fira sans", sans-serif;
}
</style>
