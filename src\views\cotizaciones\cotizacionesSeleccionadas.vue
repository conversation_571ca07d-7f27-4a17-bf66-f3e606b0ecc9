<template>
  <div class="container-fluid min-vh-100">
    <UserHeader title="Cotizaciones Seleccionadas" icon="check2-circle" />

    <main class="container-fluid rounded shadow-xl py-4 px-4 animate-fade">
      <!-- Lista de cotizaciones seleccionadas -->
      <div class="cotizaciones-section mb-4">
        <h3 class="mb-3">Cotizaciones Listas para Enviar</h3>

        <div v-if="store.cotizacionesSeleccionadas.length === 0" class="alert alert-info">
          No hay cotizaciones seleccionadas. Vuelva a la lista de cotizaciones para seleccionar algunas.
        </div>

        <div v-else class="table-responsive">
          <table class="table table-striped table-hover">
            <thead>
              <tr>
                <th>Nombre</th>
                <th>Fecha de Creación</th>
                <th>Proveedores</th>
                <th>Materiales</th>
                <th>Acciones</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(cotizacion, index) in store.cotizacionesSeleccionadas" :key="cotizacion.id">
                <td>{{ cotizacion.nombre }}</td>
                <td>{{ formatDate(cotizacion.fechaCreacion) }}</td>
                <td>{{ cotizacion.proveedores.length }}</td>
                <td>{{ cotizacion.items.length }}</td>
                <td>
                  <div class="btn-group">
                    <button class="btn btn-sm btn-success me-2" @click="enviarCotizacionIndividual(cotizacion, index)">
                      <i class="pi pi-send"></i> Enviar
                    </button>
                    <button class="btn btn-sm btn-outline-danger" @click="deseleccionarCotizacion(index)">
                      <i class="pi pi-times"></i> Quitar
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Botones de acción -->
        <div class="d-flex justify-content-end mt-3">
          <button
            class="btn btn-success me-2"
            @click="enviarCotizaciones"
            :disabled="store.cotizacionesSeleccionadas.length === 0"
          >
            <i class="pi pi-send me-2"></i> Enviar Cotizaciones
          </button>

          <button class="btn btn-secondary" @click="navigateTo('listaCotizaciones')">
            <i class="pi pi-arrow-left me-2"></i> Volver a la Lista
          </button>
        </div>
      </div>

      <!-- Botones inferiores a la derecha -->
      <div class="bottom-buttons">
        <GradientButton @click="navigateTo('listaCotizaciones')" class="nav-button">
          <i class="pi pi-arrow-left me-2"></i> Regresar
        </GradientButton>
        <GradientButton @click="navigateTo('Home')" class="nav-button">
          <i class="pi pi-home me-2"></i> Menú Inicial
        </GradientButton>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import UserHeader from "@/components/layout/headers/header.vue";
import GradientButton from "@/components/ui/GradientButton.vue";
import { useCotizacionStore } from "@/stores/cotizacionStore";
import Swal from "sweetalert2";
import type { Cotizacion } from "@/interfaces/CotizacionInterface";
import { enviarCotizacion } from "@/services/cotizacionService";

const router = useRouter();
const store = useCotizacionStore();

// Función para formatear fechas
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString();
}

// Deseleccionar una cotización
function deseleccionarCotizacion(index: number): void {
  store.deseleccionarCotizacion(index);
}

// Enviar cotizaciones seleccionadas
async function enviarCotizaciones(): Promise<void> {
  if (store.cotizacionesSeleccionadas.length === 0) {
    Swal.fire({
      title: "Error",
      text: "No hay cotizaciones seleccionadas para enviar",
      icon: "error",
      confirmButtonText: "Entendido"
    });
    return;
  }

  Swal.fire({
    title: "Enviando cotizaciones",
    text: "Por favor espere...",
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });

  try {
    await store.enviarCotizacionesSeleccionadas();

    Swal.fire({
      title: "¡Éxito!",
      text: "Las cotizaciones han sido enviadas correctamente",
      icon: "success",
      confirmButtonText: "Continuar"
    }).then(() => {
      navigateTo("nuevaCotizacion");
    });
  } catch (error) {
    Swal.fire({
      title: "Error",
      text: "Hubo un problema al enviar las cotizaciones",
      icon: "error",
      confirmButtonText: "Entendido"
    });
  }
}

// Enviar una cotización individual
async function enviarCotizacionIndividual(cotizacion: Cotizacion, index: number): Promise<void> {
  Swal.fire({
    title: `Enviando cotización "${cotizacion.nombre}"`,
    text: "Por favor espere...",
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });

  try {
    console.log(`Enviando cotización individual ${cotizacion.nombre} al backend...`);
    await enviarCotizacion(cotizacion);
    console.log(`Cotización ${cotizacion.nombre} enviada con éxito`);

    // Eliminar la cotización de la lista de cotizaciones en proceso
    const indexEnProceso = store.cotizacionesEnProceso.findIndex(c => c.id === cotizacion.id);
    if (indexEnProceso !== -1) {
      store.eliminarCotizacionEnProceso(indexEnProceso);
    }

    // Deseleccionar la cotización
    // store.deseleccionarCotizacion(index);

    Swal.fire({
      title: "¡Éxito!",
      text: `La cotización "${cotizacion.nombre}" ha sido enviada correctamente`,
      icon: "success",
      confirmButtonText: "Continuar"
    });
  } catch (error) {
    console.error(`Error al enviar la cotización ${cotizacion.nombre}:`, error);
    Swal.fire({
      title: "Error",
      text: `Hubo un problema al enviar la cotización "${cotizacion.nombre}"`,
      icon: "error",
      confirmButtonText: "Entendido"
    });
  }
}

// Función para navegar a diferentes vistas
function navigateTo(route: string) {
  router.push({ name: route });
}
</script>

<style scoped>
.container-fluid {
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.519);
}

.cotizaciones-section {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 2px 4px 6px rgba(0, 0, 0, 0.253);
}

.cotizaciones-section:hover {
  box-shadow: 2px 4px 6px rgba(184, 181, 10, 0.553);
}

.bottom-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  width: 100%;
}

.nav-button {
  min-width: 150px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1rem;
  transition: all 0.2s ease;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .bottom-buttons {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
