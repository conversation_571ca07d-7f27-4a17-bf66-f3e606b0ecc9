<template>
  <div class="table-wrapper">
    <table class="table">
      <thead>
        <tr>
          <th>ID</th>
          <th>Nombre Empresa</th>
          <th>Email</th>
          <th>País</th>
          <th>Estado</th>
          <th>Acciones</th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="(prov, index) in paginatedProveedores"
          :key="index"
          class="animate-fade animate-once animate-duration-[1000ms] animate-delay-200"
        >
          <td>{{ prov.supplierId }}</td>
          <td>{{ prov.companyName }}</td>
          <td>{{ prov.email || prov.companyOwnerEmail || 'No informado' }}</td>
          <td>{{ prov.country }}</td>
          <td>
            <span :class="getEstadoClass(prov.status)">
              {{ prov.status || 'Pendiente' }}
            </span>
          </td>
          <td>
            <div class="d-flex gap-2 justify-content-center">
              <button class="btn btn-sm btn-outline-primary" @click="mostrarDetallesModal(prov)">
                <i class="bi bi-eye"></i> Ver
              </button>
              <button class="btn btn-sm btn-outline-warning" @click="indicarErrores(prov)">
                <i class="bi bi-gear"></i> Gestionar
              </button>
              <button class="btn btn-sm btn-outline-danger" @click="$emit('excluir', prov)">
                <i class="bi bi-trash"></i> Eliminar
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <Paginator
    :rows="pageSize"
    :totalRecords="props.proveedores.length"
    :rowsPerPageOptions="[5, 10, 20]"
    @page="onPageChange"
  />
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import type { Proveedor } from "@/interfaces/ProveedorInterface";
import Paginator from "primevue/paginator";
import Swal from "sweetalert2";

const props = defineProps<{
  proveedores: Proveedor[];
}>();

const emit = defineEmits(["excluir", "cambiarEstado", "verDetalles"]);

const pageSize = ref(5);
const currentPage = ref(1);

const paginatedProveedores = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  return props.proveedores.slice(start, start + pageSize.value);
});

function onPageChange(event: any) {
  currentPage.value = event.page + 1;
  pageSize.value = event.rows;
}

// Función para obtener la clase CSS según el estado
function getEstadoClass(status: string | undefined) {
  if (!status) return 'badge bg-secondary';

  switch(status) {
    case 'Activo':
      return 'badge bg-success'; // Verde para proveedores activos
    case 'Creado':
      return 'badge bg-primary'; // Azul para proveedores recién creados
    case 'ActProveedor':
      return 'badge bg-warning text-dark'; // Amarillo para actualizados por proveedor
    case 'ActSourcing':
      return 'badge bg-info text-dark'; // Celeste para actualizados por sourcing
    default:
      return 'badge bg-secondary'; // Gris para otros estados
  }
}

// Mostrar detalles del proveedor en un Swal
function mostrarDetallesModal(p: Proveedor) {
  console.log('Mostrando detalles del proveedor:', p);

  // Formatear la fecha de creación
  const fechaCreacion = p.createdAt ? new Date(p.createdAt).toLocaleDateString() : 'No especificada';

  // Crear el contenido HTML para el Swal
  const contenidoHTML = `
    <div class="text-start">
      <div class="mb-4">
        <h5 class="border-bottom pb-2">Información General</h5>
        <div class="row">
          <div class="col-md-6 mb-2"><strong>ID:</strong> ${p.supplierId || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Nombre:</strong> ${p.companyName || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Email:</strong> ${p.email || p.companyOwnerEmail || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>País:</strong> ${p.country || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Ciudad:</strong> ${p.city || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Dirección:</strong> ${p.address || 'No especificada'}</div>
          <div class="col-md-6 mb-2"><strong>Estado:</strong> <span class="${getEstadoClass(p.status)}">${p.status || 'No definido'}</span></div>
          <div class="col-md-6 mb-2"><strong>Fecha de creación:</strong> ${fechaCreacion}</div>
        </div>
      </div>

      <div class="mb-4">
        <h5 class="border-bottom pb-2">Información de Contacto</h5>
        <div class="row">
          <div class="col-md-6 mb-2"><strong>Nombre del Dueño:</strong> ${p.companyOwnerName || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Email del Dueño:</strong> ${p.companyOwnerEmail || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Teléfono del Dueño:</strong> ${p.companyOwnerPhone || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Nombre de Contacto:</strong> ${p.contactName || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Teléfono:</strong> ${p.phone || 'No especificado'}</div>
        </div>
      </div>

      <div class="mb-4">
        <h5 class="border-bottom pb-2">Información Bancaria</h5>
        <div class="row">
          <div class="col-md-6 mb-2"><strong>Términos de Pago:</strong> ${p.paymentsTerms || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Moneda:</strong> ${p.currency || 'No especificada'}</div>
          <div class="col-md-6 mb-2"><strong>Banco:</strong> ${p.bankName || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Cuenta:</strong> ${p.accountNumber || 'No especificada'}</div>
        </div>
      </div>

      <div class="mb-4">
        <h5 class="border-bottom pb-2">Información Legal</h5>
        <div class="row">
          <div class="col-md-6 mb-2"><strong>Código de Crédito Social:</strong> ${p.socialCreditCode || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Código de Licencia de Exportación:</strong> ${p.exportLicenseCode || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Estado del Perfil:</strong> ${p.companyProfileStatus || 'No especificado'}</div>
        </div>
      </div>
    </div>
  `;

  // Mostrar el Swal con los detalles
  Swal.fire({
    title: p.companyName || 'Detalles del Proveedor',
    html: contenidoHTML,
    width: '800px',
    showCloseButton: true,
    showConfirmButton: false,
    customClass: {
      container: 'swal-wide',
      popup: 'swal-wide-popup',
      htmlContainer: 'text-start'
    }
  });
}



function indicarErrores(p: Proveedor) {
  // Determinar qué acciones mostrar según el estado actual
  let opciones: string[] = [];

  if (p.status === 'Creado') {
    opciones = [
      'Solicitar completar perfil',
      'Marcar como ActSourcing'
    ];
  } else if (p.status === 'ActProveedor') {
    opciones = [
      'Solicitar correcciones',
      'Aprobar y activar proveedor',
      'Marcar como ActSourcing'
    ];
  } else if (p.status === 'ActSourcing') {
    opciones = [
      'Solicitar actualización al proveedor',
      'Aprobar y activar proveedor'
    ];
  } else if (p.status === 'Activo') {
    opciones = [
      'Solicitar actualización al proveedor',
      'Marcar como ActSourcing'
    ];
  }

  Swal.fire({
    title: `Gestión de ${p.companyName}`,
    html: `
      <div class="text-start mb-4">
        <p><strong>Estado actual:</strong> <span class="${getEstadoClass(p.status)}">${p.status}</span></p>
        <p>Selecciona la acción que deseas realizar:</p>
      </div>
    `,
    input: 'select',
    inputOptions: opciones.reduce((obj, opt, index) => {
      obj[index] = opt;
      return obj;
    }, {} as Record<string, string>),
    inputPlaceholder: 'Selecciona una acción',
    showCancelButton: true,
    confirmButtonText: 'Continuar',
    cancelButtonText: 'Cancelar',
    inputValidator: (value) => {
      if (!value) {
        return 'Debes seleccionar una acción';
      }
      return null;
    }
  }).then((result) => {
    if (result.isConfirmed) {
      const accionSeleccionada = opciones[parseInt(result.value)];

      // Determinar el nuevo estado basado en la acción seleccionada
      let nuevoEstado = p.status;
      let requiereComentario = false;

      if (accionSeleccionada === 'Solicitar completar perfil' ||
          accionSeleccionada === 'Solicitar correcciones' ||
          accionSeleccionada === 'Solicitar actualización al proveedor') {
        nuevoEstado = 'ActSourcing';
        requiereComentario = true;
      } else if (accionSeleccionada === 'Aprobar y activar proveedor') {
        nuevoEstado = 'Activo';
      } else if (accionSeleccionada === 'Marcar como ActSourcing') {
        nuevoEstado = 'ActSourcing';
      }

      // Si se requiere comentario, mostrar un segundo modal
      if (requiereComentario) {
        Swal.fire({
          title: 'Indica los cambios requeridos',
          input: 'textarea',
          inputPlaceholder: 'Describe los cambios que debe realizar el proveedor...',
          showCancelButton: true,
          confirmButtonText: 'Enviar',
          cancelButtonText: 'Cancelar',
          inputValidator: (value) => {
            if (!value) {
              return 'Debes proporcionar información sobre los cambios requeridos';
            }
            return null;
          }
        }).then((comentarioResult) => {
          if (comentarioResult.isConfirmed) {
            // Emitir evento para cambiar el estado con el comentario
            emit('cambiarEstado', {
              proveedor: p,
              nuevoEstado,
              comentario: comentarioResult.value
            });

            Swal.fire('Solicitud enviada', `Se ha enviado la solicitud de cambios al proveedor ${p.companyName}`, 'success');
          }
        });
      } else {
        // Cambiar estado sin comentario
        emit('cambiarEstado', {
          proveedor: p,
          nuevoEstado,
          comentario: ''
        });

        Swal.fire('Estado actualizado', `El proveedor ${p.companyName} ahora está en estado ${nuevoEstado}`, 'success');
      }
    }
  });
}
</script>

<style scoped>
.table-wrapper {
  overflow-x: auto;
  padding: 0.5rem;
  max-height: 60vh;
}

.table th,
.table td {
  padding: 0.75rem;
  text-align: center;
  vertical-align: middle;
}

.badge {
  padding: 0.3rem 0.6rem;
  border-radius: 0.4rem;
  font-size: 0.85rem;
  display: inline-block;
  width: 110px; /* Ancho fijo para todas las etiquetas de estado */
  text-align: center;
}

/* Estilos para el Swal de detalles */
:deep(.swal-wide) {
  z-index: 1060;
}

:deep(.swal-wide-popup) {
  max-width: 800px;
  width: 90% !important;
  padding: 1.5rem;
}

:deep(.text-start) {
  text-align: left !important;
}

:deep(.row) {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

:deep(.col-md-6) {
  flex: 0 0 50%;
  max-width: 50%;
  padding-right: 15px;
  padding-left: 15px;
}

:deep(.mb-2) {
  margin-bottom: 0.5rem !important;
}

:deep(.mb-4) {
  margin-bottom: 1.5rem !important;
}

:deep(.pb-2) {
  padding-bottom: 0.5rem !important;
}

:deep(.border-bottom) {
  border-bottom: 1px solid #dee2e6 !important;
}
</style>
