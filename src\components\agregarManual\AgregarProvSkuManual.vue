<template>
  <div class="d-container mb-4">
    <div class="mb-4">
      <div class="d-container">
        <div class="d-flex align-items-center mb-3">
          <i class="pi pi-user me-2"></i>
          <h5 class="fw-bold mb-0">Agregar Proveedores Manualmente</h5>
        </div>

        <!-- Botón inicial -->
        <button
          v-if="!mostrarFormulario"
          class="btn custom-hover-outline mt-3"
          @click="mostrarFormulario = true"
        >
          + Agregar nuevo proveedor
        </button>

        <!-- Formulario que se muestra al hacer clic -->
        <div v-else class="mt-3">
          <!-- Botones en una fila -->
          <div class="d-flex mb-2">
            <button class="btn btn-sm" @click="mostrarFormulario = false">
              <i class="pi pi-times"></i>
            </button>
          </div>

          <div class="d-flex w-50">
            <!-- Input ocupa toda la línea -->

            <input
              v-model="nuevoEmailProveedor"
              type="email"
              class="form-control"
              placeholder="Correo del nuevo proveedor"
            />
            <button class="btn" @click="agregarProveedorNuevo">
              <i class="pi pi-user-plus"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="d-container ">
      <!-- Tabla SKUs nuevos -->
      <div class="mt-4">
        <div class="d-flex align-items-center mb-3">
          <i class="pi pi-tags me-2"></i>
          <h5 class="fw-bold mb-0">Agregar Materiales Manualmente</h5>
        </div>
        <NewSkuTable />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useCotizacionStore } from "@/stores/cotizacionStore";
import type { Proveedor } from "@/interfaces/ProveedorInterface";
import NewSkuTable from "@/components/tables/NewSkuTable.vue";
const store = useCotizacionStore();

const nuevoEmailProveedor = ref("");
const mostrarFormulario = ref(false);

function agregarProveedorNuevo() {
  if (!nuevoEmailProveedor.value) return;

  const proveedor: Proveedor = {
    email: nuevoEmailProveedor.value,
    tipo: "nuevo",
    correoPersonalizado: {
      subject: `Cotización: ${store.nombre}`,
      body: `Estimado proveedor, se le ha enviado una nueva cotización.`,
      cc: [],
    },
  };
  store.agregarProveedor(proveedor);
  nuevoEmailProveedor.value = "";
}
</script>

<style lang="css" scoped>
.section-container {
  animation: fadeIn 0.4s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.d-container {
  border: 1px solid #ffffffc6;
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: #e5e5e5dc;
  margin-top: 2rem;
}

.d-container:hover {
  background-color: conic-gradient(#ffe100, #ba8b00, #ffdd1c) border-box;
  box-shadow: 0 0 0 0.3rem rgba(255, 188, 34, 0.25);
}

.pi-tags,
.pi-user,
.pi-user-plus {
  font-size: 2rem;
}

.pi-user-plus:hover {
  color: var(--primary);
}

.pi-times {
  transition: all 0.3s ease;
}
.pi-times:hover {
  color: red;
  transform: scale(1.2);
  border: 1px solid red;
}
.custom-hover-outline {
  border: 1px solid #050505;
}

.custom-hover-outline:hover {
  border-color: var(--primary);
  color: var(--primary);
}
/* Estilos para los inputs y selects */
input {
  background-color: #fefefe0b;
  border-bottom: 0.2rem solid #0000005e;
}

input:focus,
select:focus {
  outline: none;
  border-color: var(--secondary);
  box-shadow: 0 0 0 0.2rem rgba(255, 188, 34, 0.25);
}

input:hover,
select:hover {
  outline: none;
  border-color: var(--secondary);
  box-shadow: 0 0 0 0.2rem rgba(255, 188, 34, 0.25);
}
</style>
