<template>
  <div
    v-if="visible"
    class="modal-overlay"
  >
    <div class="modal-container">
      <!-- Encabezado del modal -->
      <div class="modal-header">
        <h2 class="modal-title">Detalle de Cotización: {{ cotizacion?.nombre }}</h2>
        <button class="btn-close" @click="$emit('close')">×</button>
      </div>

      <!-- Contenido del modal -->
      <div class="modal-content">
        <!-- Información general de la cotización -->
        <div class="info-section">
          <h3>Información General</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">ID:</span>
              <span class="info-value">{{ cotizacion?.id }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Fecha Creación:</span>
              <span class="info-value">{{ formatDate(cotizacion?.fechaCreacion) }}</span>
            </div>

            <div class="info-item">
              <span class="info-label">Tipo:</span>
              <span class="info-value">{{ cotizacion?.tipo }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Estado:</span>
              <span class="info-value" :class="'estado-' + cotizacion?.estado">
                {{ cotizacion?.estado }}
              </span>
            </div>
          </div>
        </div>

        <!-- Proveedores -->
        <div class="proveedores-section">
          <h3>Proveedores</h3>

          <div v-if="!cotizacion?.proveedores || cotizacion.proveedores.length === 0" class="no-data">
            No hay proveedores asociados a esta cotización.
          </div>

          <div v-else class="accordion">
            <div
              v-for="(proveedor, index) in cotizacion.proveedores"
              :key="index"
              class="accordion-item"
            >
              <div class="accordion-header">
                <div class="proveedor-info">
                  <span class="proveedor-nombre">
                    {{ proveedor.companyName || proveedor.email || 'Proveedor sin nombre' }}
                  </span>
                  <span class="proveedor-pais">{{ proveedor.country || 'País no especificado' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Items de la cotización -->
        <div class="items-section">
          <h3>Materiales</h3>

          <div v-if="!cotizacion?.items || cotizacion.items.length === 0" class="no-data">
            No hay materiales asociados a esta cotización.
          </div>

          <div v-else class="skus-table-container">
            <table class="skus-table">
              <thead>
                <tr>
                  <th>Material</th>
                  <th>Descripción</th>
                  <th>Proveedor</th>
                  <th>Cantidad</th>
                  <th>Precio</th>
                  <th>Acciones</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, itemIndex) in cotizacion.items" :key="itemIndex">
                  <td>{{ item.material }}</td>
                  <td>{{ item.madreDescripcion || 'Sin descripción' }}</td>
                  <td>{{ item.proveedorDescripcion || 'No especificado' }}</td>
                  <td>{{ item.cantidadPedido || 0 }}</td>
                  <td>{{ item.precioNetoPedido ? `$${item.precioNetoPedido}` : 'No especificado' }}</td>
                  <td>
                    <button
                      class="btn-edit-sku"
                      @click="editarItem(item, itemIndex)"
                    >
                      <i class="pi pi-pencil"></i>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="item-actions">
            <button
              class="btn-add-sku"
              @click="agregarItem()"
            >
              <i class="pi pi-plus"></i> Agregar Material
            </button>
          </div>
        </div>
      </div>

      <!-- Pie del modal -->
      <div class="modal-footer">
        <button
          v-if="cotizacionModificada"
          class="btn-primary"
          @click="guardarCambios"
        >
          Guardar Cambios
        </button>
        <button class="btn-secondary" @click="$emit('close')">Cerrar</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { Cotizacion } from '@/interfaces/CotizacionInterface';
import type { Item } from '@/interfaces/ItemsInterface';
import Swal from 'sweetalert2';
import 'primeicons/primeicons.css';

const props = defineProps<{
  visible: boolean;
  cotizacion: Cotizacion | null;
}>();

const emit = defineEmits(['close', 'update', 'cotizacion-modificada']);

// Estado para el acordeón de proveedores
const expandedProveedores = ref<number[]>([]);

// Estado para rastrear si la cotización ha sido modificada
const cotizacionModificada = ref(false);

// Función para formatear fechas
function formatDate(dateString?: string): string {
  if (!dateString) return 'No especificada';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  } catch (error) {
    return dateString;
  }
}

// Función para expandir/contraer un proveedor en el acordeón
function toggleProveedor(index: number) {
  const currentIndex = expandedProveedores.value.indexOf(index);
  if (currentIndex === -1) {
    expandedProveedores.value.push(index);
  } else {
    expandedProveedores.value.splice(currentIndex, 1);
  }
}

// Función para editar un item
function editarItem(item: Item, itemIndex: number) {
  Swal.fire({
    title: 'Editar Material',
    html: `
      <div class="swal-form">
        <div class="swal-form-group">
          <label for="material">Material</label>
          <input id="material" class="swal2-input" value="${item.material || ''}">
        </div>
        <div class="swal-form-group">
          <label for="descripcion">Descripción</label>
          <input id="descripcion" class="swal2-input" value="${item.madreDescripcion || ''}">
        </div>
        <div class="swal-form-group">
          <label for="proveedor">Proveedor</label>
          <input id="proveedor" class="swal2-input" value="${item.proveedorDescripcion || ''}">
        </div>
        <div class="swal-form-group">
          <label for="cantidad">Cantidad</label>
          <input id="cantidad" type="number" class="swal2-input" value="${item.cantidadPedido || 0}">
        </div>
        <div class="swal-form-group">
          <label for="precio">Precio</label>
          <input id="precio" type="number" step="0.01" class="swal2-input" value="${item.precioNetoPedido || 0}">
        </div>
      </div>
    `,
    focusConfirm: false,
    showCancelButton: true,
    confirmButtonText: 'Guardar',
    cancelButtonText: 'Cancelar',
    preConfirm: () => {
      const material = (document.getElementById('material') as HTMLInputElement).value;
      const descripcion = (document.getElementById('descripcion') as HTMLInputElement).value;
      const proveedor = (document.getElementById('proveedor') as HTMLInputElement).value;
      const cantidad = parseInt((document.getElementById('cantidad') as HTMLInputElement).value);
      const precio = parseFloat((document.getElementById('precio') as HTMLInputElement).value);

      return { material, descripcion, proveedor, cantidad, precio };
    }
  }).then((result) => {
    if (result.isConfirmed && props.cotizacion) {
      const values = result.value as { material: string; descripcion: string; proveedor: string; cantidad: number; precio: number };

      // Actualizar el item con los nuevos valores
      if (props.cotizacion.items) {
        props.cotizacion.items[itemIndex] = {
          ...props.cotizacion.items[itemIndex],
          material: Number(values.material),
          madreDescripcion: values.descripcion,
          proveedorDescripcion: values.proveedor,
          cantidadPedido: values.cantidad,
          precioNetoPedido: values.precio
        };

        // Marcar la cotización como modificada
        cotizacionModificada.value = true;
        if (props.cotizacion) {
          props.cotizacion.modificada = true;
        }
      }
    }
  });
}

// Función para agregar un nuevo item a la cotización
function agregarItem() {
  Swal.fire({
    title: 'Agregar Nuevo Material',
    html: `
      <div class="swal-form">
        <div class="swal-form-group">
          <label for="material">Material</label>
          <input id="material" class="swal2-input" placeholder="Código del material">
        </div>
        <div class="swal-form-group">
          <label for="descripcion">Descripción</label>
          <input id="descripcion" class="swal2-input" placeholder="Descripción del material">
        </div>
        <div class="swal-form-group">
          <label for="proveedor">Proveedor</label>
          <input id="proveedor" class="swal2-input" placeholder="Nombre del proveedor">
        </div>
        <div class="swal-form-group">
          <label for="cantidad">Cantidad</label>
          <input id="cantidad" type="number" class="swal2-input" value="1">
        </div>
        <div class="swal-form-group">
          <label for="precio">Precio</label>
          <input id="precio" type="number" step="0.01" class="swal2-input" value="0">
        </div>
      </div>
    `,
    focusConfirm: false,
    showCancelButton: true,
    confirmButtonText: 'Agregar',
    cancelButtonText: 'Cancelar',
    preConfirm: () => {
      const material = (document.getElementById('material') as HTMLInputElement).value;
      const descripcion = (document.getElementById('descripcion') as HTMLInputElement).value;
      const proveedor = (document.getElementById('proveedor') as HTMLInputElement).value;
      const cantidad = parseInt((document.getElementById('cantidad') as HTMLInputElement).value);
      const precio = parseFloat((document.getElementById('precio') as HTMLInputElement).value);

      if (!material) {
        Swal.showValidationMessage('El código del material es obligatorio');
        return false;
      }

      return { material, descripcion, proveedor, cantidad, precio };
    }
  }).then((result) => {
    if (result.isConfirmed && props.cotizacion) {
      const values = result.value as { material: string; descripcion: string; proveedor: string; cantidad: number; precio: number };

      // Inicializar el array de items si no existe
      if (!props.cotizacion.items) {
        props.cotizacion.items = [];
      }

      // Agregar el nuevo item
      props.cotizacion.items.push({
        material: Number(values.material),
        madreDescripcion: values.descripcion,
        proveedorDescripcion: values.proveedor,
        cantidadPedido: values.cantidad,
        precioNetoPedido: values.precio,
        level1: "",
        level2: "",
        level3: "",
        level4: "",
        temporada: "",
        textoTemporada: "",
        grupoAnalisisDescripcion: "",
        proveedorCod: "",
        fob: 0,
        sku: 0,
        madreId: "",
        feriaId: ""
      });

      // Marcar la cotización como modificada
      cotizacionModificada.value = true;
      if (props.cotizacion) {
        props.cotizacion.modificada = true;
      }
    }
  });
}

// Función para guardar los cambios en la cotización
function guardarCambios() {
  if (!props.cotizacion) return;

  Swal.fire({
    title: 'Guardar Cambios',
    text: 'Ingrese una descripción de los cambios realizados para notificar a los proveedores:',
    input: 'textarea',
    inputPlaceholder: 'Descripción de los cambios...',
    showCancelButton: true,
    confirmButtonText: 'Guardar y Notificar',
    cancelButtonText: 'Cancelar',
    inputValidator: (value) => {
      if (!value) {
        return 'Debe ingresar una descripción de los cambios';
      }
      return null;
    }
  }).then((result) => {
    if (result.isConfirmed) {
      // Emitir evento para actualizar la cotización en el componente padre
      emit('update', {
        cotizacion: props.cotizacion,
        descripcionCambios: result.value
      });

      // Notificar que la cotización ha sido modificada
      emit('cotizacion-modificada', props.cotizacion?.id);

      // Cerrar el modal
      emit('close');

      // Mostrar mensaje de éxito
      Swal.fire(
        '¡Cambios guardados!',
        'Los proveedores serán notificados de los cambios realizados.',
        'success'
      );
    }
  });
}

// Resetear el estado cuando cambia la cotización
watch(() => props.cotizacion, () => {
  expandedProveedores.value = [];
  cotizacionModificada.value = false;
}, { immediate: true });
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.modal-title {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
}

.btn-close:hover {
  color: #343a40;
}

.modal-content {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.info-section {
  margin-bottom: 2rem;
}

.info-section h3 {
  margin-bottom: 1rem;
  color: #495057;
  font-size: 1.2rem;
  border-bottom: 2px solid #f8f9fa;
  padding-bottom: 0.5rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-weight: bold;
  color: #6c757d;
  font-size: 0.9rem;
}

.info-value {
  font-size: 1rem;
  color: #212529;
}

.estado-pendiente {
  color: #ffc107;
}

.estado-aprobado {
  color: #28a745;
}

.estado-rechazado {
  color: #dc3545;
}

.proveedores-section {
  margin-bottom: 1rem;
}

.proveedores-section h3 {
  margin-bottom: 1rem;
  color: #495057;
  font-size: 1.2rem;
  border-bottom: 2px solid #f8f9fa;
  padding-bottom: 0.5rem;
}

.accordion {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.accordion-item {
  border-bottom: 1px solid #e9ecef;
}

.accordion-item:last-child {
  border-bottom: none;
}

.accordion-header {
  padding: 1rem;
  background-color: #f8f9fa;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s;
}

.accordion-header:hover {
  background-color: #e9ecef;
}

.proveedor-info {
  display: flex;
  flex-direction: column;
}

.proveedor-nombre {
  font-weight: bold;
  color: #495057;
}

.proveedor-pais {
  font-size: 0.9rem;
  color: #6c757d;
}

.accordion-icon {
  color: #6c757d;
  font-size: 0.8rem;
}

.accordion-content {
  padding: 1rem;
  background-color: white;
}

.no-data {
  padding: 1rem;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

.skus-table-container {
  overflow-x: auto;
  margin-bottom: 1rem;
}

.skus-table {
  width: 100%;
  border-collapse: collapse;
}

.skus-table th,
.skus-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.skus-table th {
  background-color: #f8f9fa;
  font-weight: bold;
  color: #495057;
}

.skus-table tr:hover {
  background-color: #f8f9fa;
}

.btn-edit-sku {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.btn-edit-sku:hover {
  background-color: #e9ecef;
}

.proveedor-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.btn-add-sku {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-add-sku:hover {
  background-color: #218838;
}

.btn-primary {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: #0069d9;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

/* Estilos para el formulario en SweetAlert */
:deep(.swal-form) {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

:deep(.swal-form-group) {
  display: flex;
  flex-direction: column;
  text-align: left;
}

:deep(.swal-form-group label) {
  margin-bottom: 0.25rem;
  font-weight: bold;
  color: #495057;
}

:deep(.swal2-input) {
  margin: 0 !important;
}
</style>
