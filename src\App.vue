<template>
  <div class="app">
    <!-- Sidebar
    <Sidebar /> -->
    <!-- Vista renderizada -->
    <main class="p-2 flex-grow-1">
      <router-view />
    </main>
  </div>
</template>

<script setup lang="ts">
// import Sidebar from "@/components/layout/sidebars/Sidebar.vue";

</script>

<style lang="scss">
:root {
  --primary: #ffbc22;
  --secondary: #ffe95d;
  --dark: #222222dc;
  --dark-alt: #ffa504;
  --light: #ffff7a;
  --light-alt: #ffffff;
  --sidebar-width: 250px;
  --primary-hover: #ffbd22b9;
  --alt: #ecab0f;
  --alt-2: #f7d547;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Fira sans", sans-serif;
  
}

.app {
  display: flex;
  min-height: 100vh;
  background-image: url("@/assets/images/background_alt3.jpg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat; 
}
html, body {
  width: 100%;
  overflow-x: hidden;
}

main {
  flex: 1;
  min-width: 0; // ⚠️ Importante para que no se desborde en flex
  overflow-x: auto; // por si hay tablas u otros contenidos grandes
}

h1,
h2,
h3,
h4,
h5,
label,
h6 {
  color: #4e4e4e;
}
</style>
