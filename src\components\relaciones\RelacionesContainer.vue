<template>
  <div class="relaciones-container animate-fade">
    <div class="relaciones-header">
      <h4 class="fw-bold">
        <i class="pi pi-link me-2"></i> Relaciones para el elemento seleccionado
      </h4>
      <button class="btn btn-sm btn-outline-danger" @click="$emit('cerrar')">
        <i class="pi pi-times"></i>
      </button>
    </div>

    <div class="relaciones-info">
      <div class="info-item">
        <span class="info-label">Madre ID:</span>
        <span class="info-value">{{ itemConvertido.madreId }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Descripción:</span>
        <span class="info-value">{{ itemConvertido.madreDescripcion }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Proveedor:</span>
        <span class="info-value">{{ itemConvertido.proveedorDescripcion }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Material:</span>
        <span class="info-value">{{ itemConvertido.material }}</span>
      </div>
    </div>

    <div class="relaciones-content">
      <div class="row">
        <div class="col-md-6 mb-4 mb-md-0">
          <MadreProveedores
            :mapeoBackend="itemConvertido"
            @seleccionarProveedores="agregarProveedoresSeleccionados"
          />
        </div>
        <div class="col-md-6">
          <ProveedorMadres
            :mapeoBackend="itemConvertido"
            @seleccionarMadres="agregarMadresSeleccionados"
          />
        </div>
      </div>
    </div>

    <div class="relaciones-footer">
      <div class="seleccion-resumen">
        <span class="badge bg-primary me-2">
          {{ proveedoresSeleccionados.length }} proveedores seleccionados
        </span>
        <span class="badge bg-success">
          {{ madresSeleccionadas.length }} materiales seleccionados
        </span>
      </div>
      <button
        class="btn btn-success"
        @click="agregarACotizacion"
        :disabled="!haySelecciones"
      >
        <i class="pi pi-plus-circle me-2"></i> Crear Cotización
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import MadreProveedores from './MadreProveedores.vue';
import ProveedorMadres from './ProveedorMadres.vue';
import type { Item } from '@/interfaces/ItemsInterface';
// import type { Proveedor } from '@/interfaces/ProveedorInterface';
import Swal from 'sweetalert2';

const props = defineProps<{
  item: Item;
}>();

function mapBackendToItem(data: any): Item {
  return {
    material: data.material ?? 0,
    cantidadPedido: data.cantidadPedido ?? 0,
    precioNetoPedido: data.precioNetoPedido ?? 0,
    level1: data.level1 ?? '',
    level2: data.level2 ?? '',
    level3: data.level3 ?? '',
    level4: data.level4 ?? '',
    temporada: data.temporada ?? '',
    textoTemporada: data.textoTemporada ?? '',
    grupoAnalisisDescripcion: data.grupoAnalisis ?? '',
    fob: data.fob ?? 0,
    sku: data.sku ?? 0,
    feriaId: data.feriaId ?? '',
    madreId: data.madreId ?? '',
    madreDescripcion: data.madreDescripcion ?? '',
    proveedorCod: data.acreedor ?? '',
    proveedorDescripcion: data.nombreProveedor ?? '',
    imagen: data.imagen ?? undefined,
  };
}
const itemConvertido = ref<Item>(mapBackendToItem(props.item));

const emit = defineEmits<{
  (e: 'cerrar'): void;
  (e: 'agregarACotizacion', data: { proveedores: Item[], items: Item[] }): void;
}>();

const proveedoresSeleccionados = ref<Item[]>([]);
const madresSeleccionadas = ref<Item[]>([]);

// Observar cambios en el item seleccionado
watch(
  () => props.item,
  (nuevoValor) => {
    itemConvertido.value = mapBackendToItem(nuevoValor);
    // console.log("Item mapeado:", itemConvertido.value);

    // Limpiar las selecciones cuando cambia el item

  },
  { immediate: true }
);

function agregarProveedoresSeleccionados(nuevos: Item[]) {
  if (nuevos.length === 0) return;

  const nuevo = nuevos[0];

  if (proveedoresSeleccionados.value.length > 0) {
    const proveedorActual = proveedoresSeleccionados.value[0];

    // Mostrar confirmación con SweetAlert
    Swal.fire({
      title: '¿Deseas reemplazar el proveedor seleccionado?',
      html: `
        <p><strong>Proveedor actual:</strong> ${proveedorActual.proveedorCod} | ${proveedorActual.proveedorDescripcion}</p>
        <p><strong>Nuevo proveedor:</strong> ${nuevo.proveedorCod} | ${nuevo.proveedorDescripcion}</p>
      `,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Sí, reemplazar',
      cancelButtonText: 'Cancelar'
    }).then((result) => {
      if (result.isConfirmed) {
        proveedoresSeleccionados.value = [nuevo];
      }
    });
  } else {
    // Si no hay proveedor, simplemente lo agregamos
    proveedoresSeleccionados.value = [nuevo];
  }
}

function agregarMadresSeleccionados(nuevos: Item[]){
  nuevos.forEach(nuevo => {
    const yaExiste = madresSeleccionadas.value.some(p => p.madreId === nuevo.madreId && p.sku === nuevo.sku ) ;
    if (!yaExiste) {
      madresSeleccionadas.value.push(nuevo);
    }
  });
}

// Computed para verificar si hay selecciones
const haySelecciones = computed(() => {
  return proveedoresSeleccionados.value.length > 0 && madresSeleccionadas.value.length > 0;
});

// Función para agregar las selecciones a la cotización
function agregarACotizacion() {
  // Emitir evento para mantener compatibilidad con el componente padre
  emit('agregarACotizacion', {
    proveedores: proveedoresSeleccionados.value,
    items: madresSeleccionadas.value
  });

  // Imprimir en consola lo que se está agregando
  // console.log("Agregando a cotización:", {
  //   proveedores: proveedoresSeleccionados.value,
  //   items: madresSeleccionadas.value
  // });
}
</script>

<style scoped>
.relaciones-container {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  margin: 2rem 0;
  overflow: hidden;
}

.relaciones-header {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 1.25rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.relaciones-info {
  background-color: #f8f9fa;
  padding: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.info-item {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.info-label {
  font-weight: 600;
  font-size: 0.9rem;
  color: #6c757d;
}

.info-value {
  font-size: 1.1rem;
}

.relaciones-content {
  padding: 1.5rem;
}

.relaciones-footer {
  background-color: #f8f9fa;
  padding: 1.25rem;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.seleccion-resumen {
  display: flex;
  align-items: center;
}

.badge {
  font-size: 0.9rem;
  padding: 0.5rem 0.75rem;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade {
  animation: fadeIn 0.4s ease-in-out;
}

@media (max-width: 768px) {
  .relaciones-footer {
    flex-direction: column;
    gap: 1rem;
  }

  .seleccion-resumen {
    width: 100%;
    justify-content: center;
    margin-bottom: 0.5rem;
  }
}
</style>
