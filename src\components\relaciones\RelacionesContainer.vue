<template>
  <div class="relaciones-container animate-fade">
    <div class="relaciones-header">
      <h4 class="fw-bold">
        <i class="pi pi-link me-2"></i> Relaciones para el elemento seleccionado
      </h4>
      <button class="btn btn-sm btn-outline-danger" @click="$emit('cerrar')">
        <i class="pi pi-times"></i>
      </button>
    </div>

    <div class="relaciones-info">
      <div class="info-item">
        <span class="info-label">Madre ID:</span>
        <span class="info-value">{{ itemConvertido.madreId }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Descripción:</span>
        <span class="info-value">{{ itemConvertido.madreDescripcion }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Proveedor:</span>
        <span class="info-value">{{ itemConvertido.proveedorDescripcion }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Material:</span>
        <span class="info-value">{{ itemConvertido.material }}</span>
      </div>
    </div>

    <div class="relaciones-content">
      <div class="row">
        <div class="col-md-6 mb-4 mb-md-0">
          <MadreProveedores
            :mapeoBackend="itemConvertido"
            @seleccionarProveedores="agregarProveedoresSeleccionados"
          />
        </div>
        <div class="col-md-6">
          <ProveedorMadres
            :mapeoBackend="itemConvertido"
            @seleccionarMadres="agregarMadresSeleccionados"
          />
        </div>
      </div>
    </div>

    <!-- Sección de proveedores seleccionados -->
    <div v-if="proveedoresSeleccionados.length > 0" class="proveedores-seleccionados">
      <h6 class="fw-bold mb-3">
        <i class="pi pi-check-circle me-2"></i> Proveedores Seleccionados
      </h6>
      <div class="proveedores-grid">
        <div
          v-for="(proveedor, index) in proveedoresSeleccionados"
          :key="proveedor.proveedorCod"
          class="proveedor-seleccionado"
        >
          <div class="proveedor-info">
            <span class="proveedor-codigo">{{ proveedor.proveedorCod }}</span>
            <span class="proveedor-nombre">{{ proveedor.proveedorDescripcion }}</span>
          </div>
          <button
            class="btn btn-sm btn-outline-danger"
            @click="removerProveedor(index)"
            title="Remover proveedor"
          >
            <i class="pi pi-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Sección de madres seleccionadas -->
    <div v-if="madresSeleccionadas.length > 0" class="madres-seleccionadas">
      <h6 class="fw-bold mb-3">
        <i class="pi pi-check-circle me-2"></i> Materiales Seleccionados
      </h6>
      <div class="madres-grid">
        <div
          v-for="(madre, index) in madresSeleccionadas"
          :key="`${madre.madreId}-${madre.sku}`"
          class="madre-seleccionada"
        >
          <div class="madre-info">
            <span class="madre-codigo">{{ madre.madreId }}</span>
            <span class="madre-descripcion">{{ madre.madreDescripcion }}</span>
            <span v-if="madre.sku" class="madre-sku">SKU: {{ madre.sku }}</span>
          </div>
          <button
            class="btn btn-sm btn-outline-danger"
            @click="removerMadre(index)"
            title="Remover material"
          >
            <i class="pi pi-times"></i>
          </button>
        </div>
      </div>
    </div>

    <div class="relaciones-footer">
      <div class="seleccion-resumen">
        <span class="badge bg-primary me-2">
          {{ proveedoresSeleccionados.length }} proveedores seleccionados
        </span>
        <span class="badge bg-success">
          {{ madresSeleccionadas.length }} materiales seleccionados
        </span>
      </div>
      <button
        class="btn btn-success"
        @click="agregarACotizacion"
        :disabled="!haySelecciones"
      >
        <i class="pi pi-plus-circle me-2"></i> Crear Cotización
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import MadreProveedores from './MadreProveedores.vue';
import ProveedorMadres from './ProveedorMadres.vue';
import type { Item } from '@/interfaces/ItemsInterface';
// import type { Proveedor } from '@/interfaces/ProveedorInterface';
import Swal from 'sweetalert2';

const props = defineProps<{
  item: Item;
}>();

function mapBackendToItem(data: any): Item {
  return {
    material: data.material ?? 0,
    cantidadPedido: data.cantidadPedido ?? 0,
    precioNetoPedido: data.precioNetoPedido ?? 0,
    level1: data.level1 ?? '',
    level2: data.level2 ?? '',
    level3: data.level3 ?? '',
    level4: data.level4 ?? '',
    temporada: data.temporada ?? '',
    textoTemporada: data.textoTemporada ?? '',
    grupoAnalisisDescripcion: data.grupoAnalisis ?? '',
    fob: data.fob ?? 0,
    sku: data.sku ?? 0,
    feriaId: data.feriaId ?? '',
    madreId: data.madreId ?? '',
    madreDescripcion: data.madreDescripcion ?? '',
    proveedorCod: data.acreedor ?? '',
    proveedorDescripcion: data.nombreProveedor ?? '',
    imagen: data.imagen ?? undefined,
  };
}
const itemConvertido = ref<Item>(mapBackendToItem(props.item));

const emit = defineEmits<{
  (e: 'cerrar'): void;
  (e: 'agregarACotizacion', data: { proveedores: Item[], items: Item[] }): void;
}>();

const proveedoresSeleccionados = ref<Item[]>([]);
const madresSeleccionadas = ref<Item[]>([]);

// Observar cambios en el item seleccionado
watch(
  () => props.item,
  (nuevoValor) => {
    itemConvertido.value = mapBackendToItem(nuevoValor);
    // console.log("Item mapeado:", itemConvertido.value);

    // Limpiar las selecciones cuando cambia el item

  },
  { immediate: true }
);

function agregarProveedoresSeleccionados(nuevos: Item[]) {
  if (nuevos.length === 0) return;

  // Agregar cada proveedor nuevo que no esté ya en la lista
  nuevos.forEach(nuevo => {
    const yaExiste = proveedoresSeleccionados.value.some(p =>
      p.proveedorCod === nuevo.proveedorCod
    );

    if (!yaExiste) {
      proveedoresSeleccionados.value.push(nuevo);
    }
  });

  // Mostrar mensaje de confirmación
  if (nuevos.length === 1) {
    Swal.fire({
      title: 'Proveedor agregado',
      text: `Se ha agregado el proveedor: ${nuevos[0].proveedorDescripcion}`,
      icon: 'success',
      timer: 2000,
      showConfirmButton: false
    });
    console.log("Se ha agregado un proveedor:", nuevos[0].proveedorDescripcion);
  } else {
    Swal.fire({
      title: 'Proveedores agregados',
      text: `Se han agregado ${nuevos.length} proveedores`,
      icon: 'success',
      timer: 2000,
      showConfirmButton: false
    });
  }
}

function removerProveedor(index: number) {
  const proveedorRemovido = proveedoresSeleccionados.value[index];
  proveedoresSeleccionados.value.splice(index, 1);

  Swal.fire({
    title: 'Proveedor removido',
    text: `Se ha removido el proveedor: ${proveedorRemovido.proveedorDescripcion}`,
    icon: 'info',
    timer: 2000,
    showConfirmButton: false
  });
}

function removerMadre(index: number) {
  const madreRemovida = madresSeleccionadas.value[index];
  madresSeleccionadas.value.splice(index, 1);

  Swal.fire({
    title: 'Material removido',
    text: `Se ha removido el material: ${madreRemovida.madreDescripcion}`,
    icon: 'info',
    timer: 2000,
    showConfirmButton: false
  });
}

function agregarMadresSeleccionados(nuevos: Item[]){
  nuevos.forEach(nuevo => {
    const yaExiste = madresSeleccionadas.value.some(p => p.madreId === nuevo.madreId && p.sku === nuevo.sku ) ;
    if (!yaExiste) {
      madresSeleccionadas.value.push(nuevo);
    }
      if (nuevos.length === 1) {
    Swal.fire({
      title: 'Madre agregada',
      text: `Se ha agregado la madre: ${nuevos[0].madreDescripcion}`,
      icon: 'success',
      timer: 2000,
      showConfirmButton: false
    });
  } else {
    Swal.fire({
      title: 'Madres agregadas',
      text: `Se han agregado ${nuevos.length} madres`,
      icon: 'success',
      timer: 2000,
      showConfirmButton: false
    });
  }
  });
}

// Computed para verificar si hay selecciones
const haySelecciones = computed(() => {
  return proveedoresSeleccionados.value.length > 0 && madresSeleccionadas.value.length > 0;
});

// Función para agregar las selecciones a la cotización
function agregarACotizacion() {
  // Emitir evento para mantener compatibilidad con el componente padre
  emit('agregarACotizacion', {
    proveedores: proveedoresSeleccionados.value,
    items: madresSeleccionadas.value
  });

  // Imprimir en consola lo que se está agregando
  // console.log("Agregando a cotización:", {
  //   proveedores: proveedoresSeleccionados.value,
  //   items: madresSeleccionadas.value
  // });
}
</script>

<style scoped>
.relaciones-container {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  margin: 2rem 0;
  overflow: hidden;
}

.relaciones-header {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 1.25rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.relaciones-info {
  background-color: #f8f9fa;
  padding: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.info-item {
  display: flex;
  flex-direction: column;
  min-width: 200px;
}

.info-label {
  font-weight: 600;
  font-size: 0.9rem;
  color: #6c757d;
}

.info-value {
  font-size: 1.1rem;
}

.relaciones-content {
  padding: 1.5rem;
}

.proveedores-seleccionados {
  background-color: #f8f9fa;
  padding: 1.25rem;
  border-top: 1px solid #dee2e6;
}

.proveedores-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.proveedor-seleccionado {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 0.75rem;
  min-width: 250px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.proveedor-seleccionado:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.proveedor-info {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.proveedor-codigo {
  font-weight: 600;
  font-size: 0.9rem;
  color: #495057;
}

.proveedor-nombre {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.madres-seleccionadas {
  background-color: #f8f9fa;
  padding: 1.25rem;
  border-top: 1px solid #dee2e6;
}

.madres-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.madre-seleccionada {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 0.75rem;
  min-width: 280px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.madre-seleccionada:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.madre-info {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.madre-codigo {
  font-weight: 600;
  font-size: 0.9rem;
  color: #495057;
}

.madre-descripcion {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.madre-sku {
  font-size: 0.75rem;
  color: #28a745;
  margin-top: 0.25rem;
  font-weight: 500;
}

.relaciones-footer {
  background-color: #f8f9fa;
  padding: 1.25rem;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.seleccion-resumen {
  display: flex;
  align-items: center;
}

.badge {
  font-size: 0.9rem;
  padding: 0.5rem 0.75rem;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade {
  animation: fadeIn 0.4s ease-in-out;
}

@media (max-width: 768px) {
  .relaciones-footer {
    flex-direction: column;
    gap: 1rem;
  }

  .seleccion-resumen {
    width: 100%;
    justify-content: center;
    margin-bottom: 0.5rem;
  }
}
</style>
