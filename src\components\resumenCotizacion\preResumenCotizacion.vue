<template>
  <div>
    <!-- Proveedores agregados -->
    <div class="d-container mb-4">
      <div class="d-flex align-items-center mb-3">
        <i class="pi pi-users me-2"></i>
        <h5 class="fw-bold m-0">Proveedores agregados</h5>
      </div>

      <div v-if="store.proveedores.length === 0" class="alert alert-info">
        No hay proveedores agregados aún. Utilice las opciones "Buscar" o
        "Agregar Manualmente" para incluir proveedores.
      </div>

      <ul v-else class="list-group">
        <li
          v-for="(p, idx) in store.proveedores"
          :key="'prov-' + idx"
          class="list-group-item mb-2 d-flex justify-content-between align-items-center"
        >
          <div>
            <h6 class="fw-bold text-primary mb-0">
              {{ p.companyName || p.companyOwnerName || p.email }}
            </h6>
            <small class="text-muted">{{
              p.country || "País no especificado"
            }}</small>
          </div>
          <button
            class="btn btn-sm btn-outline-danger"
            @click="store.eliminarProveedor(idx)"
          >
            ✖
          </button>
        </li>
      </ul>
    </div>

    <!-- Items de la cotización -->
    <div class="d-container">
      <div class="d-flex align-items-center mb-3">
        <i class="pi pi-file me-2"></i>
        <h5 class="fw-bold mb-0">Materiales en la cotización</h5>
      </div>

      <div v-if="!hayItems" class="alert alert-info">
        No hay Materiales agregados a la cotización. Utilice las opciones "Buscar" o
        "Agregar Manualmente" para incluir Materiales.
      </div>

      <div v-else class="d-flex flex-column gap-4">
        <div class="border rounded p-3 shadow-sm hoverable-card">
          <h6 class="text-primary fw-semibold mb-3">
            Lista de materiales
          </h6>

          <ul class="list-group">
            <li
              v-for="(item, idx) in store.items"
              :key="'item-' + idx"
              class="list-group-item mb-2 d-flex justify-content-between align-items-center"
            >
              <div>
                <span class="fw-semibold">{{ item.material }}</span>
                <small class="text-muted d-block">Proveedor: {{ item.proveedorDescripcion || 'No especificado' }}</small>
              </div>
              <button
                class="btn btn-sm btn-outline-danger"
                @click="store.eliminarItem(idx)"
              >
                ✖
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useCotizacionStore } from "@/stores/cotizacionStore";

const store = useCotizacionStore();

const hayItems = computed(() => {
  return store.items.length > 0;
});
</script>

<style lang="css" scoped>
/* Estilos para las secciones */
.section-container {
  animation: fadeIn 0.4s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.d-container {
  border: 1px solid #ffffffc6;
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: #e5e5e5dc;
}

.d-container:hover {
  animation: 2s rotate linear infinite;
  background-color: conic-gradient( #ffe100, #ba8b00, #ffdd1c)
    border-box;
  box-shadow: 0 0 0 0.3rem rgba(255, 188, 34, 0.25);
}

.pi-users,
.pi-file {
  font-size: 2rem;
}

.alert {
  background-color: rgba(255, 255, 255, 0.8);
  border-left: 4px solid #0093e9;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 0.25rem;
}

.alert-info {
  border-left-color: #0093e9;
}
</style>
