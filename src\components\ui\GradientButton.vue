<template>
    <button class="gradient-button" :class="{ 'disabled': disabled }" :disabled="disabled" @click="handleClick">
      <span><slot /></span>
    </button>
  </template>

  <script setup lang="ts">
  import { defineProps, defineEmits } from 'vue';

  const props = defineProps<{
    disabled?: boolean;
  }>();

  const emit = defineEmits<{
    (e: 'click', event: MouseEvent): void;
  }>();

  function handleClick(event: MouseEvent) {
    if (!props.disabled) {
      emit('click', event);
    }
  }
  </script>

  <style scoped>
  .gradient-button {
    align-items: center;
    background-image: linear-gradient(144deg, var(--secondary), var(--dark-alt), var(--primary));
    border: 0;
    border-radius: 8px;
    box-sizing: border-box;
    color:rgb(255, 255, 255);
    display: flex;
    font-size: 18px;
    justify-content: center;
    line-height: 1em;
    max-width: 100%;
    min-width: 300px;
    padding: 3px;
    text-decoration: none;
    user-select: none;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s;
  }

  .gradient-button:not(.disabled):active,
  .gradient-button:not(.disabled):hover {
    outline: 0;
    box-shadow: #ffe644 0 2px 5px 2px;
  }

  .gradient-button.disabled {
    opacity: 0.7;
    cursor: not-allowed;
    background-image: linear-gradient(144deg, #a0a0a0, #c0c0c0 50%, #d0d0d0);
  }

  .gradient-button span {
    background-color: rgba(231, 231, 231, 0.381);
    padding: 16px 24px;
    border-radius: 6px;
    width: 100%;
    height: 100%;
    transition: 300ms;
    text-align: center;
  }

  .gradient-button:not(.disabled):hover span {
    background: none;
  }

  .gradient-button:not(.disabled):active {
    transform: scale(0.9);
  }
  </style>
