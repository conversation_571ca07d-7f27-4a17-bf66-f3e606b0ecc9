import { createRouter, createWebHistory } from 'vue-router';
import Home from '@/views/Home.vue';

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: Home,
    },
    {
      path: '/cotizaciones',
      name: 'Cotizaciones',
      component: () => import('@/views/cotizaciones/Cotizaciones.vue'),
    },
    {
      path: '/cotizaciones/nueva',
      name: 'nuevaCotizacion',
      component: () => import('@/views/cotizaciones/nuevaCotizacion.vue'),
    },
    {
      path: '/cotizaciones/filtro-seleccion',
      name: 'filtroSeleccion',
      component: () => import('@/views/cotizaciones/filtroSeleccion.vue'),
    },
    {
      path: '/cotizaciones/lista',
      name: 'listaCotizaciones',
      component: () => import('@/views/cotizaciones/listaCotizaciones.vue'),
    },
    {
      path: '/cotizaciones/seleccionadas',
      name: 'cotizacionesSeleccionadas',
      component: () => import('@/views/cotizaciones/cotizacionesSeleccionadas.vue'),
    },
    {
      path: '/cotizaciones/view',
      name: 'CotizacionesView',
      component: () => import('@/views/cotizaciones/CotizacionesView.vue'),
    },
    {
      path: '/cotizaciones/comparador',
      name: 'ComparadorProveedores',
      component: () => import('@/views/cotizaciones/ComparadorProveedores.vue'),
    },
    {
      path: '/proveedores',
      name: 'Proveedores',
      component: () => import('@/views/proveedores/menuProveedores.vue'),
    },
    {
      path: '/nuevoProveedor/mail',
      name: 'envioCorreoProveedor',
      component: () => import('@/views/mail/EnvioCorreoAProveedor.vue'),
    },
    {
      path: '/proveedores/listaProveedores',
      name: 'Lista de Proveedores',
      component: () => import('@/views/proveedores/listaProveedores.vue'),
    },
    {
      path: '/report',
      component: () => import('@/views/reportes/DashBoardReports.vue')
    },
    {
      path: '/enviocorreo',
      name: 'envioCorreos',
      props: true,
      component: () => import('@/components/mail/EnvioCorreos.vue')
    }
  ],
});

export default router;