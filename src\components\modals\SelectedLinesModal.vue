<template>
  <div class="modal-overlay" v-if="visible" @click.self="closeModal">
    <div class="modal-container">
      <div class="modal-header">
        <h4 class="modal-title">Líneas Seleccionadas</h4>
        <button class="close-button" @click="closeModal">
          <i class="pi pi-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <DataTable 
          :value="items" 
          :paginator="true" 
          :rows="5"
          :rowsPerPageOptions="[10, 20, 50]"
          tableStyle="min-width: 50rem;"
          class="p-datatable-sm"
        >
          <Column header="Imagen" class="text-center">
            <template #body="slotProps">
              <div class="image-container">
                <img
                  v-if="slotProps.data.imagen"
                  :src="slotProps.data.imagen"
                  class="sku-image"
                  @click="openImageModal(slotProps.data.imagen)"
                />
                <div v-else class="no-image">Sin imagen</div>
              </div>
            </template>
          </Column>
          <Column field="sku" header="SKU" class="text-center" />
          <Column field="nombreProveedor" header="Proveedor" class="text-center" />
          <Column field="descripcionMadre" header="Descripción" class="text-center" />
          <Column field="material" header="Material" class="text-center" />
          <Column field="cantidadPedido" header="Cantidad" class="text-center" />
          <Column field="precioNetoPedido" header="Precio Neto (USD)" class="text-center">
            <template #body="slotProps">
              {{ formatCurrency(slotProps.data.precioNetoPedido) }}
            </template>
          </Column>
          <Column field="fob" header="FOB" class="text-center">
            <template #body="slotProps">
              {{ formatCurrency(slotProps.data.fob) }}
            </template>
          </Column>
        </DataTable>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="closeModal">Cancelar</button>
        <button class="btn btn-primary" @click="confirmSelection">Confirmar</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import type { Item } from '@/interfaces/ItemsInterface';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Swal from 'sweetalert2';

const props = defineProps<{
  visible: boolean;
  items: Item[];
}>();

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'confirm', items: Item[]): void;
}>();

function closeModal() {
  emit('close');
}

function confirmSelection() {
  emit('confirm', props.items);
}

function formatCurrency(value: number): string {
  return new Intl.NumberFormat('es-CL', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
}

// Función para abrir un modal con la imagen ampliada
function openImageModal(imageUrl: string) {
  if (!imageUrl) return;

  Swal.fire({
    imageUrl,
    imageAlt: "Imagen del SKU",
    width: "auto",
    padding: "1em",
    showConfirmButton: false,
    showCloseButton: true,
  });
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.modal-title {
  margin: 0;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #6c757d;
}

.modal-body {
  padding: 1rem;
  overflow-y: auto;
  flex-grow: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #e9ecef;
}

/* Estilos para las imágenes */
.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  margin: 0 auto;
}

.sku-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #dee2e6;
  transition: transform 0.2s;
}

.sku-image:hover {
  transform: scale(1.1);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.no-image {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.8rem;
  color: #6c757d;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px dashed #dee2e6;
}
</style>
