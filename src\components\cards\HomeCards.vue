<template>
    <div
      class="col-md-5 card p-4 mb-3 align-items-center cards animate-fade-left animate-once animate-duration-1000 animate-delay-[500ms]"
      @click="router.push(route)"
    >
      <i :class="`${icon} display-4 ${color}`"></i>
      <h2 class="card-title">{{ title }}</h2>
      <p class="card-text">{{ subtitle }}</p>
      <p v-if="extra" class="card-subtext text-center fw-bold">{{ extra }}</p>
    </div>
  </template>
  
  <script setup lang="ts">
  import { useRouter } from 'vue-router'
  
  const router = useRouter()
  
  defineProps<{
    icon: string
    title: string
    subtitle: string
    extra?: string
    route: string
    color?: string
  }>()
  </script>
  
  <style scoped>
  .card {
    cursor: pointer;
    transition: transform 0.2s ease-in-out, background-color 0.2s ease-in-out;
    background-color: rgba(255, 255, 255, 0.197);
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  }
  
  .card:hover {
    transform: scale(1.05);
    background-color: #e9e9e999;
  }
  </style>