import { createApp } from 'vue';
import App from '@/App.vue';
import router from '@/router';
import '@/assets/style.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap';
import PrimeVue from 'primevue/config';
import Aura from '@primevue/themes/aura';
import { createPinia } from 'pinia';
import keycloak from '@/auth/keycloak';

keycloak.init({ onLoad: 'login-required' }).then((authenticated: boolean) => {
  if (!authenticated) {
    console.warn("User not authenticated!");
    window.location.reload();
  } else {
    localStorage.setItem('access_token', keycloak.token || '');
    const app = createApp(App);

    app.use(createPinia());
    app.use(router);
    app.use(PrimeVue, {
      theme: {
        preset: Aura,
        options: {
          darkModeSelector: false || 'none',
        }
      }
    });

    // Acceso global a Keycloak
    app.config.globalProperties.$keycloak = keycloak;

    app.mount('#app');
  }
}).catch((err: Error) => {
    console.error("Keycloak initialization failed", err.message);
  });
