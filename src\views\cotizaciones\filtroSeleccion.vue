<template>
  <div class="container min-vh-100">
    <!-- <UserHeader title="Selección de Proveedores y MadreId" icon="search" /> -->

    <main class="container rounded shadow-xl py-4 px-4 animate-fade">
      <!-- Sección de búsqueda y filtros -->
      <div class="d-container mb-4">
        <!-- <div class="d-flex align-items-center mb-3">
          <i class="pi pi-users me-2"></i>
          <h5 class="fw-bold mb-0">Buscar</h5>
        </div> -->

        <div class="mb-4">
          <div class="d-flex gap-2">
            <label class="form-label fw-semibold">Buscar por:</label>
            <select v-model="tipoBusqueda" class="form-select filterSize">
              <option disabled value="">Seleccione una opción</option>
              <option
                v-for="filtro in filtros"
                :key="filtro.id"
                :value="filtro.valor"
              >
                {{ filtro.etiqueta }}
              </option>
            </select>
            <input
              v-model="valorBusqueda"
              class="form-control filterSize"
              placeholder="Ingrese un valor"
              @keyup.enter="agregarFiltro"
              />

              <button class="btn btn-outline-primary" @click="agregarFiltro">
                Agregar Filtro
              </button>

              <button
                v-if="Object.keys(filtrosSeleccionados).length"
                class="btn btn-outline-danger"
                @click="limpiarFiltros"
              >
                Limpiar Filtros
              </button>

              <button
                v-if="resultados.length"
                class="btn btn-outline-danger"
                @click="limpiarResultados"
              >
                Cerrar Tabla
              </button>

              <button
                class="btn btn-search"
                @click="handleBuscar"
                :disabled="!Object.keys(filtrosSeleccionados).length">
                <i class="pi pi-search"></i>
              </button>
          </div>
        </div>

        <!-- Filtros aplicados -->

        <div>
          <div class="filtros-aplicados mb-4"
            v-if="Object.keys(filtrosSeleccionados).length"
          >
          <div class="filtros-tags">
              <h6 class="fw-bold mb-3">Filtros aplicados:</h6>
              <div
                v-for="(valor, campo) in filtrosSeleccionados"
                :key="campo"
                class="filtro-tag"
              >
                <span class="filtro-nombre">{{ obtenerEtiquetaFiltro(campo) }}</span>
                <span class="filtro-valor">"{{ valor }}"</span>
                <button
                  class="btn-remove"
                  @click="limpiarFiltroUnico(campo)"
                  title="Eliminar filtro"
                >
                  <i class="pi pi-times"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <small v-if="sinResultados" class="text-danger mt-1 d-block text-center">
          No se encontraron resultados para tu búsqueda.
        </small>

        <!-- Tabla de resultados -->
        <div  class="tablaRef" ref="tablaRef" v-if="resultados.length">
          <ResultadosTabla
            :resultados="resultados"
            @seleccionar="mostrarRelaciones"
          />
        </div>
      </div>

      <!-- Componente de relaciones (visible cuando se selecciona un item) -->
      <RelacionesContainer
        v-if="itemSeleccionado"
        :item="itemSeleccionado"
        @cerrar="itemSeleccionado = null"
        @agregarACotizacion="agregarACotizacion"
      />

      <!-- Botón para volver a la página principal -->
      <div class="d-flex">
        <div class="bottom-buttons">
          <div class="button-container">
            <GradientButton
              @click="navigateTo('nuevaCotizacion')"
              class="nav-button"
              >
              <i class="pi pi-file-plus me-2"></i> Volver a Nueva Cotización
            </GradientButton>
          </div>
          <div class="button-container">
            <GradientButton
            @click="navigateTo('listaCotizaciones')"
            class="nav-button"
            >
            Ver Cotizaciones en Proceso <i class="pi pi-arrow-right ms-2"></i>
            </GradientButton>
          </div>
          <div class="button-container">
            <GradientButton
            @click="navigateTo('Home')"
            class="nav-button"
            >
            <i class="pi pi-home me-2"></i> Menú Inicial
            </GradientButton>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import UserHeader from "@/components/layout/headers/header.vue";
import GradientButton from "@/components/ui/GradientButton.vue";
import ResultadosTabla from "@/components/tables/SkuTable.vue";
import RelacionesContainer from "@/components/relaciones/RelacionesContainer.vue";
import { useFiltroBusqueda } from "@/composables/useFiltroBusqueda";
import { useCotizacionStore } from "@/stores/cotizacionStore";
import type { Item } from "@/interfaces/ItemsInterface";
import type { Proveedor } from "@/interfaces/ProveedorInterface";
import Swal from "sweetalert2";
import "primeicons/primeicons.css";
import { SupplierService } from '@/services/newSupplierService';


const router = useRouter();
const store = useCotizacionStore();
const tablaRef = ref<HTMLElement | null>(null);
const itemSeleccionado = ref<Item | null>(null);

// Composable para la búsqueda
const {
  tipoBusqueda,
  filtros,
  valorBusqueda,
  filtrosSeleccionados,
  resultados,
  sinResultados,
  agregarFiltro,
  obtenerEtiquetaFiltro,
  realizarBusqueda,
  limpiarResultados,
  obtenerFiltrosDisponibles,
} = useFiltroBusqueda();

function navigateTo(route: string) {
  router.push({ name: route });
}

onMounted(() => {
  obtenerFiltrosDisponibles();
  if (
    resultados.value.length === 0 &&
    Object.keys(filtrosSeleccionados.value).length
  ) {
    realizarBusqueda(); // recarga si no hay resultados pero sí filtros
  }
});

// Función para mostrar las relaciones cuando se selecciona un item
function mostrarRelaciones(items: Item[]) {
  // console.log('datos seleccionados', items);

  // Si hay items seleccionados, tomamos el primero (o podríamos manejar múltiples de otra forma)
  if (items && items.length > 0) {
    itemSeleccionado.value = items[0]; // Tomamos el primer item seleccionado

    // Scroll hacia el componente de relaciones
    nextTick(() => {
      window.scrollTo({
        top: document.body.scrollHeight,
        behavior: "smooth",
      });
    });
  } else {
    console.warn('No se seleccionaron items');
  }
}


// Función para agregar a la cotización (proveedores y/o materiales)
async function agregarACotizacion(data: { proveedores: Item[], items: Item[] }) {

  console.log('Proveedores y madres', data.proveedores, data.items)
  // Generar un nombre temporal para la cotización
  const nombreTemporal = `Cotización`;
  store.nombre = nombreTemporal;

  // Agregar proveedores
  await Promise.all(data.proveedores.map(async item => {

    const nombreProveedor = item.proveedorDescripcion;
    const res = await SupplierService.getSupplierByCompanyName(nombreProveedor);
    console.log('resultado', res.data);

    const proveedor: Proveedor = {
      supplierId: item.proveedorCod,
      companyName: item.proveedorDescripcion,
      email: "",  // Valor por defecto
      tipo: "existente"
    };
    console.log('Proveedores', proveedor)

    const yaExiste = store.proveedores.some(
      p => p.companyName?.toLowerCase() === proveedor.companyName?.toLowerCase()
    );

    if (!yaExiste && proveedor.companyName) {
      store.agregarProveedor(proveedor);
    }
  }));

  // Agregar items directamente a la cotización
  data.items.forEach(item => {
    store.agregarItem(item);
  });

  try {
    // Guardar la cotización automáticamente
    const cotizacion = store.guardarCotizacionEnProceso();
    console.log('Proveedores de la cotización', cotizacion.suppliers)

    // Mostrar mensaje de éxito con opción para avanzar
    const mensaje = `Se ha creado la cotización "${cotizacion.quotation}" con ${data.proveedores.length} proveedores y ${data.items.length} materiales`;

    // Usar SweetAlert2 para mostrar un mensaje más elegante con opciones
    Swal.fire({
      title: '¡Cotización Creada!',
      text: mensaje,
      icon: 'success',
      showCancelButton: true,
      confirmButtonText: 'Ver cotizaciones',
      cancelButtonText: 'Crear otra'
    }).then((result: { isConfirmed: boolean }) => {
      if (result.isConfirmed) {
        // Si el usuario elige avanzar, lo llevamos a la vista de cotizaciones en proceso
        router.push({ name: 'listaCotizaciones' });
      } else {
        // Si el usuario elige continuar, solo cerramos el componente de relaciones
        itemSeleccionado.value = null;
      }
    });
  } catch (error) {
    // Mostrar mensaje de error
    Swal.fire({
      title: 'Error',
      text: error instanceof Error ? error.message : 'Error al crear la cotización',
      icon: 'error',
      confirmButtonText: 'Entendido'
    });
  }

  // Cerrar el componente de relaciones
  itemSeleccionado.value = null;
}

function limpiarFiltros() {
  filtrosSeleccionados.value = {};
}

function limpiarFiltroUnico(campo: string) {
  // Elimina el filtro por su clave (campo)
  delete filtrosSeleccionados.value[campo];
}

async function handleBuscar() {
  await realizarBusqueda(); // Lógica de búsqueda del composable
  await nextTick(); // Esperá que el DOM se actualice

  if (tablaRef.value && resultados.value.length > 0) {
    tablaRef.value.scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  }
}

</script>

<style scoped>
.container{
  min-width: 100%;
}
/* Estilos para las secciones */
.section-container {
  animation: fadeIn 0.4s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.d-container {
  border: 1px solid #ffffffc6;
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: #e5e5e5dc;
}

.d-container:hover {
  background-color: conic-gradient(#ffe100, #ba8b00, #ffdd1c) border-box;
  box-shadow: 0 0 0 0.3rem rgba(255, 188, 34, 0.25);
}

.pi-search,
.pi-users {
  font-size: 20px;
}

.tablaRef{
  overflow: auto;
}
.pi-search {
  transition: all 0.3s ease;
  color: #929292;
  /* Asegurar que el ícono mantiene su posición centrada */
  display: block;
}

.pi-search:hover {
  transform: scale(1.2);
  color: var(--secondary);
}

option {
  background-color: #fefefedc;
}

/* Estilos para los inputs y selects */
input {
  background-color: #fefefe0b;
  border-bottom: 0.2rem solid #0000005e;
}

input:focus,
select:focus {
  outline: none;
  border-color: var(--secondary);
  box-shadow: 0 0 0 0.2rem rgba(255, 188, 34, 0.25);
}

input:hover,
select:hover {
  outline: none;
  border-color: var(--secondary);
  box-shadow: 0 0 0 0.2rem rgba(255, 188, 34, 0.25);
}

/* Estilos para los filtros aplicados */
.filtros-aplicados {
  background-color: #f8f9fa;
  justify-items: start;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filtros-tags {
  display: flex;
  flex-wrap: row wrap;
  gap: 30px;
  max-height: 30px;
}

.filtro-tag {
  display: flex;
  align-items: center;
  background: #626262;
  border-radius: 10px;
  padding: 5px 12px;
  font-size: 0.9rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  height: 30px;
}

.filtro-tag:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.btn-remove:hover {
  background: rgba(255, 0, 0, 0.4);
  color: white;
  transform: rotate(15deg) scale(1.1);
}

.filtro-nombre {
  font-weight: 600;
  margin-right: 5px;
  color: #ebebeb;
  font-size: 0.9rem;
}

.filtro-nombre::after {
  content: ":";
}

.filtro-valor {
  color: #ebebeb;
  margin-right: 4px;
  margin-left: 4px;
  font-size: 0.9rem;
}

.btn-remove {
  background: rgba(255, 0, 0, 0.2);
  border: none;
  color: #ebebeb;
  cursor: pointer;
  padding: 5px;
  font-size: 1rem;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin-left: 5px;
}

/* Estilos para el botón de búsqueda centralizado */
.btn-search {
  background: linear-gradient(135deg, #ffe030, #ca9d0a);
  border: none;
  border-radius: 25px;
  box-shadow: 0 2px 5px rgb(255, 223, 79);
  transition: all 0.3s ease;
  height: 40px;
  width: 40px;
  /* Eliminar el padding que desplaza el contenido */
  padding: 0;
  /* Añadir propiedades de flexbox para centrar */
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-search:hover:not(:disabled) {
  transform: translateY(-3px);
  scale: 1.2;
}

.btn-search:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(255, 218, 10, 0.2);
}

.btn-search:disabled {
  background: linear-gradient(135deg, #a0a0a0, #c0c0c0);
  cursor: not-allowed;
  opacity: 0.7;
}

.button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.filterSize {
  width: 250px;
}

.nav-button {
  min-width: 150px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1rem;
  transition: all 0.2s ease;
  border-radius: 8px;
}

.bottom-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  width: 100%;
}
</style>
