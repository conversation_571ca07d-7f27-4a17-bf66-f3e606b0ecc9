<template>
  <div class="container-fluid min-vh-100 py-4">
    <UserHeader title="Reportes" icon="graph-up" />

    <main class="flex-grow-1 d-flex justify-content-center align-items-center">
      <div class="container">
        <div class="row justify-content-center mt-3">
          <div class="col-md-10">
            <div class="card shadow-sm">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                  <i :class="headerIcon"></i> {{ headerTitle }}
                </h4>
                <div class="btn-group">
                  <button
                    class="btn"
                    :class="activeView === 'proveedores' ? 'btn-primary' : 'btn-outline-primary'"
                    @click="changeView('proveedores')"
                  >
                    <i class="bi bi-people-fill me-1"></i> Proveedores
                  </button>
                  <button
                    class="btn"
                    :class="activeView === 'cotizaciones' ? 'btn-primary' : 'btn-outline-primary'"
                    @click="changeView('cotizaciones')"
                  >
                    <i class="bi bi-file-earmark-text-fill me-1"></i> Cotizaciones
                  </button>
                  <button
                    class="btn"
                    :class="activeView === 'usuario' ? 'btn-primary' : 'btn-outline-primary'"
                    @click="changeView('usuario')"
                  >
                    <i class="bi bi-person-fill me-1"></i> Usuario
                  </button>
                </div>
              </div>
              <div class="card-body p-4">
                <!-- Vista de Proveedores -->
                <div v-if="activeView === 'proveedores'" class="animate-fade animate-duration-500">
                  <ChartAllSuppliers />
                </div>

                <!-- Vista de Cotizaciones -->
                <div v-if="activeView === 'cotizaciones'" class="animate-fade animate-duration-500">
                  <ChartAllQuotes />
                </div>

                <!-- Vista de Usuario -->
                <div v-if="activeView === 'usuario'" class="animate-fade animate-duration-500">
                  <div class="text-center py-5">
                    <i class="bi bi-person-circle display-1 text-muted"></i>
                    <h3 class="mt-3">Estadísticas de Usuario</h3>
                    <p class="text-muted">Próximamente: Información sobre actividad del usuario, cotizaciones creadas y más.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import UserHeader from "@/components/layout/headers/header.vue";
import ChartAllSuppliers from "@/components/charts/ChartAllSuppliers.vue";
import ChartAllQuotes from "@/components/charts/ChartAllQuotes.vue";
import keycloak from "@/auth/keycloak";
import { useAuth } from "@/composables/useAuth";
import { jwtDecode } from "jwt-decode";

const { hasRole } = useAuth();
const token = keycloak.token;

if (token) {
  const decoded: any = jwtDecode(token);
  console.log("🧾 Token Decodificado:", decoded);
  console.log("Has roles:", decoded.resource_access?.myclient?.roles);
}

const allowedRoles = ["Sourcing", "Auditor"];

onMounted(() => {
  const isAuthorized = allowedRoles.some((role) => hasRole(role));
  
  if (!isAuthorized) {
    router.push({ name: "Home" }); // o path: '/' según tu config
  }
});

const route = useRoute();
const router = useRouter();
const activeView = ref('proveedores');

// Actualizar la vista basada en el parámetro de consulta
onMounted(() => {
  const viewParam = route.query.view as string;
  if (viewParam && ['proveedores', 'cotizaciones', 'usuario'].includes(viewParam)) {
    activeView.value = viewParam;
  }
});

// Cambiar la vista y actualizar la URL
function changeView(view: string) {
  activeView.value = view;
  router.push({ query: { view } });
}

// Calcular el título e icono del encabezado según la vista activa
const headerTitle = computed(() => {
  switch (activeView.value) {
    case 'proveedores': return 'Reporte de Proveedores';
    case 'cotizaciones': return 'Reporte de Cotizaciones';
    case 'usuario': return 'Estadísticas de Usuario';
    default: return 'Reportes';
  }
});

const headerIcon = computed(() => {
  switch (activeView.value) {
    case 'proveedores': return 'bi bi-people-fill me-2';
    case 'cotizaciones': return 'bi bi-file-earmark-text-fill me-2';
    case 'usuario': return 'bi bi-person-fill me-2';
    default: return 'bi bi-graph-up me-2';
  }
});
</script>

<style scoped>
.card {
  border-radius: 12px;
  overflow: hidden;
  border: none;
}

.card-header {
  background: linear-gradient(135deg, #FFB347, #FFCC33);
  color: white;
  border-bottom: none;
  padding: 1rem 1.5rem;
}

.btn-group .btn {
  border-radius: 20px;
  margin: 0 5px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #fff;
  color: #FFB347;
  border-color: #fff;
}

.btn-outline-primary {
  color: #fff;
  border-color: #fff;
}

.btn-outline-primary:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}
</style>
