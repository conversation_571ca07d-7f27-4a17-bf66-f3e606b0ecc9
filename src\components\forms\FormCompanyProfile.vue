<template>
  <div
    class="form-company-profile form-company-profile-flex"
    style="min-height: 100vh">
    <ScrollPanel style="height: 600px; max-width: 1350px; min-width: 310px">
      <div class="form-container">
        <div class="containerSuppliers containerStyle">
          <h3 class="title">Supplier's Information</h3>
          <div class="separateTitleForm"></div>
          <Form class="justifyFormSupplier row1">
            <div class="column1">
              <label>Company Name *</label>
              <InputText
                maxlength="50"
                class="inputsize"
                v-model="form.companyName"
                name="companyName"
                type="text"
                placeholder="Company Name"
                @blur="validateSupplierField('companyName')" />
              <div class="errorSize">
                <small v-if="errors.companyName" class="text-red-500">{{
                  errors.companyName
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Company Owner Name *</label>
              <InputText
                class="inputsize"
                v-model="form.companyOwnerName"
                name="companyOwnerName"
                type="text"
                placeholder="Company Owner Name"
                @blur="validateSupplierField('companyOwnerName')" />
              <div class="errorSize">
                <small v-if="errors.companyOwnerName" class="text-red-500">{{
                  errors.companyOwnerName
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Company Owner Email *</label>
              <InputText
                class="inputsize"
                v-model="form.companyOwnerEmail"
                name="companyOwnerEmail"
                type="text"
                placeholder="Company Owner Email"
                @blur="validateSupplierField('companyOwnerEmail')" />
              <div class="errorSize">
                <small v-if="errors.companyOwnerEmail" class="text-red-500">{{
                  errors.companyOwnerEmail
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Company Owner Phone *</label>
              <InputText
                class="inputsize"
                v-model="form.companyOwnerPhone"
                name="companyOwnerPhone"
                type="text"
                placeholder="Company Owner Phone"
                @blur="validateSupplierField('companyOwnerPhone')" />
              <div class="errorSize">
                <small v-if="errors.companyOwnerPhone" class="text-red-500">{{
                  errors.companyOwnerPhone
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Social credit code/CIN *</label>
              <InputText
                class="inputsize"
                v-model="form.socialCreditCode"
                name="socialCreditCode"
                type="text"
                placeholder="Social Credit Code/CIN"
                @blur="validateSupplierField('socialCreditCode')" />
              <div class="errorSize">
                <small v-if="errors.socialCreditCode" class="text-red-500">{{
                  errors.socialCreditCode
                }}</small>
              </div>
            </div>
            <div class="column1">
              <label>Export License Code *</label>
              <InputText
                class="inputsize"
                v-model="form.exportLicenseCode"
                name="exportLicenseCode"
                placeholder="Export License Code"
                @blur="validateSupplierField('exportLicenseCode')" />
              <div class="errorSize">
                <small v-if="errors.exportLicenseCode" class="text-red-500">
                  {{ errors.exportLicenseCode }}</small
                >
              </div>
            </div>

            <div class="column1">
              <label>Address *</label>
              <InputText
                maxlength="200"
                class="inputsize"
                v-model="form.address"
                name="address"
                type="text"
                placeholder="Address"
                @blur="validateSupplierField('address')" />
              <div class="errorSize">
                <small v-if="errors.address" class="text-red-500">{{
                  errors.address
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>County/Province *</label>
              <InputText
                class="inputsize"
                v-model="form.province"
                name="province"
                placeholder="County/Province"
                @blur="validateSupplierField('province')" />
              <div class="errorSize">
                <small v-if="errors.province" class="text-red-500">{{
                  errors.province
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>City *</label>
              <InputText
                class="inputsize"
                v-model="form.city"
                name="city"
                placeholder="City"
                @blur="validateSupplierField('city')" />
              <div class="errorSize">
                <small v-if="errors.city" class="text-red-500">{{
                  errors.city
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>State *</label>
              <InputText
                class="inputsize"
                v-model="form.state"
                name="state"
                placeholder="State"
                @blur="validateSupplierField('state')" />
              <div class="errorSize">
                <small v-if="errors.state" class="text-red-500">{{
                  errors.state
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Country *</label>
              <InputText
                class="inputsize"
                v-model="form.country"
                name="country"
                placeholder="Country"
                @blur="validateSupplierField('country')" />
              <div class="errorSize">
                <small v-if="errors.country" class="text-red-500">{{
                  errors.country
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Contact Name *</label>
              <InputText
                class="inputsize"
                v-model="form.contactName"
                name="contactName"
                type="text"
                placeholder="Contact Name"
                @blur="validateSupplierField('contactName')" />
              <div class="errorSize">
                <small v-if="errors.contactName" class="text-red-500">{{
                  errors.contactName
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Phone Number *</label>
              <InputText
                class="inputsize"
                v-model="form.phone"
                name="phone"
                placeholder="Phone Number"
                @blur="validateSupplierField('phone')" />
              <div class="errorSize">
                <small v-if="errors.phone" class="text-red-500">{{
                  errors.phone
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Email Address *</label>
              <InputText
                class="inputsize"
                v-model="form.email"
                name="email"
                placeholder="Email Address"
                @blur="validateSupplierField('email')" />
              <div class="errorSize">
                <small v-if="errors.email" class="text-red-500">{{
                  errors.email
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Currency *</label>
              <Dropdown
                class="inputsize"
                v-model="form.currency"
                name="currency"
                placeholder="Currency"
                :options="currencyOptions"
                optionLabel="name"
                @blur="validateSupplierField('currency')" />
              <div class="errorSize">
                <small v-if="errors.currency" class="text-red-500">{{
                  errors.currency
                }}</small>
              </div>
            </div>
          </Form>
        </div>

        <div class="containerBank containerStyle">
          <h3>Bank Information</h3>
          <div class="separateTitleForm"></div>
          <Form class="justifyFormBank row1">
            <div class="column1">
              <label>Bank Name *</label>
              <InputText
                class="inputsize"
                v-model="bankForm.bankName"
                name="bankName"
                placeholder="Bank Name"
                @blur="validateBankField('bankName')" />
              <div class="errorSize">
                <small v-if="bankErrors.bankName" class="text-red-500">{{
                  bankErrors.bankName
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Bank Address *</label>
              <InputText
                class="inputsize"
                v-model="bankForm.bankAddress"
                name="bankAddress"
                placeholder="Bank Address"
                @blur="validateBankField('bankAddress')" />
              <div class="errorSize">
                <small v-if="bankErrors.bankAddress" class="text-red-500">{{
                  bankErrors.bankAddress
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Swift Code *</label>
              <InputText
                class="inputsize"
                v-model="bankForm.swiftCode"
                name="swiftCode"
                placeholder="Swift Code"
                @blur="validateBankField('swiftCode')" />
              <div class="errorSize">
                <small v-if="bankErrors.swiftCode" class="text-red-500">{{
                  bankErrors.swiftCode
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>IBAN Number</label>
              <InputText
                class="inputsize"
                v-model="bankForm.ibanNumber"
                name="ibanNumber"
                placeholder="IBAN Number"
                @blur="validateBankField('ibanNumber')" />
              <div class="errorSize">
                <small v-if="bankErrors.ibanNumber" class="text-red-500">{{
                  bankErrors.ibanNumber
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Account Number *</label>
              <InputText
                class="inputsize"
                v-model="bankForm.accountNumber"
                name="accountNumber"
                placeholder="Account Number"
                @blur="validateBankField('accountNumber')" />
              <div class="errorSize">
                <small v-if="bankErrors.accountNumber" class="text-red-500">{{
                  bankErrors.accountNumber
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Beneficiary Name *</label>
              <InputText
                class="inputsize"
                v-model="bankForm.beneficiaryName"
                name="beneficiaryName"
                placeholder="Beneficiary Name"
                @blur="validateBankField('beneficiaryName')" />
              <div class="errorSize">
                <small v-if="bankErrors.beneficiaryName" class="text-red-500">{{
                  bankErrors.beneficiaryName
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Beneficiary Address *</label>
              <InputText
                class="inputsize"
                v-model="bankForm.beneficiaryAddress"
                name="beneficiaryAddress"
                placeholder="Beneficiary Address"
                @blur="validateBankField('beneficiaryAddress')" />
              <div class="errorSize">
                <small
                  v-if="bankErrors.beneficiaryAddress"
                  class="text-red-500"
                  >{{ bankErrors.beneficiaryAddress }}</small
                >
              </div>
            </div>
          </Form>
        </div>

        <!-- FACTORY INFORMATION -->

        <div
          v-for="(factory, index) in factoryForms"
          :key="index"
          class="containerFactory containerStyle">
          <h3>Factory Information #{{ index + 1 }}</h3>
          <div class="separateTitleForm"></div>
          <Form class="justifyFormFactory row1">
            <div class="column1">
              <label>Factory Name *</label>
              <InputText
                class="inputsize"
                v-model="factory.factoryName"
                name="factoryName"
                placeholder="Factory Name"
                @blur="validateFactoryField('factoryName', index)" />
              <div class="errorSize">
                <small v-if="factory.errors.factoryName" class="text-red-500">{{
                  factory.errors.factoryName
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>Factory Address *</label>
              <InputText
                class="inputsize"
                v-model="factory.factoryAddress"
                name="factoryAddress"
                placeholder="Factory Address"
                @blur="validateFactoryField('factoryAddress', index)" />
              <div class="errorSize">
                <small
                  v-if="factory.errors.factoryAddress"
                  class="text-red-500"
                  >{{ factory.errors.factoryAddress }}</small
                >
              </div>
            </div>

            <div class="column1">
              <label>Country *</label>
              <InputText
                class="inputsize"
                v-model="factory.factoryCountry"
                name="factoryCountry"
                placeholder="Country"
                @blur="validateFactoryField('factoryCountry', index)" />
              <div class="errorSize">
                <small
                  v-if="factory.errors.factoryCountry"
                  class="text-red-500"
                  >{{ factory.errors.factoryCountry }}</small
                >
              </div>
            </div>

            <div class="column1">
              <label>County/Province *</label>
              <InputText
                class="inputsize"
                v-model="factory.factoryProvince"
                name="factoryProvince"
                placeholder="County/Province"
                @blur="validateFactoryField('factoryProvince', index)" />
              <div class="errorSize">
                <small
                  v-if="factory.errors.factoryProvince"
                  class="text-red-500"
                  >{{ factory.errors.factoryProvince }}</small
                >
              </div>
            </div>

            <div class="column1">
              <label>City *</label>
              <InputText
                class="inputsize"
                v-model="factory.factoryCity"
                name="factoryCity"
                placeholder="City"
                @blur="validateFactoryField('factoryCity', index)" />
              <div class="errorSize">
                <small v-if="factory.errors.factoryCity" class="text-red-500">{{
                  factory.errors.factoryCity
                }}</small>
              </div>
            </div>

            <div class="column1">
              <label>State *</label>
              <InputText
                class="inputsize"
                v-model="factory.factoryState"
                name="factoryState"
                placeholder="State"
                @blur="validateFactoryField('factoryState', index)" />
              <div class="errorSize">
                <small
                  v-if="factory.errors.factoryState"
                  class="text-red-500"
                  >{{ factory.errors.factoryState }}</small
                >
              </div>
            </div>

            <div class="column1">
              <label>ZIP Code *</label>
              <InputText
                class="inputsize"
                v-model="factory.factoryZipCode"
                name="factoryZipCode"
                placeholder="ZIP Code"
                @blur="validateFactoryField('factoryZipCode', index)" />
              <div class="errorSize">
                <small
                  v-if="factory.errors.factoryZipCode"
                  class="text-red-500"
                  >{{ factory.errors.factoryZipCode }}</small
                >
              </div>
            </div>

            <div class="column1">
              <label>Phone *</label>
              <InputText
                class="inputsize"
                v-model="factory.factoryPhone"
                name="factoryPhone"
                placeholder="Phone"
                @blur="validateFactoryField('factoryPhone', index)" />
              <div class="errorSize">
                <small
                  v-if="factory.errors.factoryPhone"
                  class="text-red-500"
                  >{{ factory.errors.factoryPhone }}</small
                >
              </div>
            </div>

            <div class="column1">
              <label>Export License Code</label>
              <InputText
                class="inputsize"
                v-model="factory.exportLicenseCode"
                name="exportLicenseCode"
                placeholder="Export License Code (Optional)" />
              <div class="errorSize">
                <small
                  v-if="factory.errors.exportLicenseCode"
                  class="text-red-500">
                  {{ factory.errors.exportLicenseCode }}</small
                >
              </div>
            </div>

            <div class="column1">
              <label>Main Product </label>
              <InputText
                class="inputsize"
                v-model="factory.mainProduction"
                name="mainProduction"
                placeholder="Main Product (Optional)" />
            </div>

            <!-- Botón para eliminar esta fábrica (excepto la primera) -->
            <div
              v-if="index > 0"
              class="column1 flex justify-content-center align-items-center">
              <Button
                icon="pi pi-trash"
                class="p-button-danger p-button-sm"
                @click="removeFactory(index)"
                label="Remove Factory" />
            </div>
          </Form>
        </div>
      </div>
    </ScrollPanel>
    <div class="controls">
      <div class="factory-controls column1">
        <div class="factory-count">
          <label>Total Factories: {{ factoryForms.length }}</label>
        </div>
        <Button
          id="btnFactory"
          @click="addFactory"
          class="btn"
          severity="secondary"
          label="Add More Factory Form">
        </Button>
      </div>

      <div v-if="formSaved" class="success-message">
        <p>Information saved successfully!</p>
      </div>
      <div class="submit-container">
        <Button
          class="submitBtn"
          @click="handleUnifiedSubmit"
          severity="primary"
          label="Save All Information" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Form } from "@primevue/forms";
import { ref, reactive } from "vue";
import { ScrollPanel } from "primevue";

// Flag to show success message
const formSaved = ref(false);

// Opciones para el dropdown de monedas
const currencyOptions = [
  { name: "CLP - Chilean Peso", code: "CLP" },
  { name: "USD - United States Dollar", code: "USD" },
  { name: "CNY - Chinese Yuan", code: "CNY" },
];

// FORMULARIO DE INFORMACIÓN DEL PROVEEDOR
const form = reactive({
  companyName: "",
  companyOwnerName: "",
  companyOwnerPhone: "",
  companyOwnerEmail: "",
  socialCreditCode: "",
  exportLicenseCode: "",
  address: "",
  province: "",
  city: "",
  state: "",
  country: "",
  contactName: "",
  phone: "",
  email: "",
  currency: null as { name: string; code: string } | null,
});

const errors = reactive({
  companyName: "",
  companyOwnerName: "",
  companyOwnerPhone: "",
  companyOwnerEmail: "",
  socialCreditCode: "",
  exportLicenseCode: "",
  address: "",
  province: "",
  city: "",
  state: "",
  country: "",
  contactName: "",
  phone: "",
  email: "",
  currency: "",
});

// FORMULARIO DE INFORMACIÓN BANCARIA
const bankForm = reactive({
  bankName: "",
  bankAddress: "",
  swiftCode: "",
  ibanNumber: "",
  accountNumber: "",
  beneficiaryName: "",
  beneficiaryAddress: "",
});

const bankErrors = reactive({
  bankName: "",
  bankAddress: "",
  swiftCode: "",
  ibanNumber: "",
  accountNumber: "",
  beneficiaryName: "",
  beneficiaryAddress: "",
});

// INTERFAZ PARA FORMULARIO DE FÁBRICA
interface FactoryFormType {
  factoryName: string;
  factoryAddress: string;
  factoryCountry: string;
  factoryProvince: string;
  factoryCity: string;
  factoryState: string;
  factoryZipCode: string;
  factoryPhone: string;
  exportLicenseCode: string;
  mainProduction: string;
  errors: {
    factoryName: string;
    factoryAddress: string;
    factoryCountry: string;
    factoryProvince: string;
    factoryCity: string;
    factoryState: string;
    factoryZipCode: string;
    factoryPhone: string;
    exportLicenseCode: string;
    mainProduction: string;
  };
}

// Función para crear un nuevo formulario de fábrica vacío
const createEmptyFactoryForm = (): FactoryFormType => {
  return {
    factoryName: "",
    factoryAddress: "",
    factoryCountry: "",
    factoryProvince: "",
    factoryCity: "",
    factoryState: "",
    factoryZipCode: "",
    factoryPhone: "",
    exportLicenseCode: "",
    mainProduction: "",
    errors: {
      factoryName: "",
      factoryAddress: "",
      factoryCountry: "",
      factoryProvince: "",
      factoryCity: "",
      factoryState: "",
      factoryZipCode: "",
      factoryPhone: "",
      exportLicenseCode: "",
      mainProduction: "",
    },
  };
};

// Array reactivo para almacenar todos los formularios de fábrica
const factoryForms = ref<FactoryFormType[]>([createEmptyFactoryForm()]);

// Función para agregar un nuevo formulario de fábrica
const addFactory = () => {
  factoryForms.value.push(createEmptyFactoryForm());
};

// Función para eliminar un formulario de fábrica
const removeFactory = (index: number) => {
  if (index > 0) {
    // No permitir eliminar la primera fábrica
    factoryForms.value.splice(index, 1);
  }
};

// Mantener la compatibilidad con el código existente
// Al usar el primer formulario para mantener compatibilidad con el código anterior

// Funciones de validación para el formulario de proveedor
const validateSupplierField = (fieldName: keyof typeof form) => {
  errors[fieldName] = "";

  // Validaciones específicas por campo
  switch (fieldName) {
    case "companyName":
      if (!form.companyName) {
        errors.companyName = "Company name is required";
      } else if (form.companyName.length < 3) {
        errors.companyName = "Company name must be at least 3 characters long";
      }
      break;

    case "companyOwnerName":
      if (!form.companyOwnerName) {
        errors.companyOwnerName = "Company owner name is required";
      }
      break;

    case "companyOwnerEmail":
      if (!form.companyOwnerEmail) {
        errors.companyOwnerEmail = "Company owner email is required";
      } else if (!validateEmail(form.companyOwnerEmail)) {
        errors.companyOwnerEmail = "Invalid email format";
      }
      break;

    case "companyOwnerPhone":
      if (!form.companyOwnerPhone) {
        errors.companyOwnerPhone = "Company owner phone is required";
      } else if (!validatePhone(form.companyOwnerPhone)) {
        errors.companyOwnerPhone = "Invalid phone number format";
      }
      break;

    case "socialCreditCode":
      if (!form.socialCreditCode) {
        errors.socialCreditCode = "Social credit code is required";
      }
      break;

    case "exportLicenseCode":
      if (!form.exportLicenseCode) {
        errors.exportLicenseCode = "Export license code is required";
      } else if (!validateExportLicenseCode(form.exportLicenseCode)) {
        errors.exportLicenseCode = "Invalid export license code format";
      }
      break;

    case "address":
      if (!form.address) {
        errors.address = "Address is required";
      } else if (form.address.length < 5) {
        errors.address = "Address must be at least 5 characters long";
      }
      break;

    case "province":
      if (!form.province) {
        errors.province = "Province is required";
      }
      break;

    case "city":
      if (!form.city) {
        errors.city = "City is required";
      }
      break;

    case "state":
      if (!form.state) {
        errors.state = "State is required";
      }
      break;

    case "country":
      if (!form.country) {
        errors.country = "Country is required";
      }
      break;

    case "contactName":
      if (!form.contactName) {
        errors.contactName = "Contact name is required";
      } else if (form.contactName.length < 3) {
        errors.contactName = "Contact name must be at least 3 characters long";
      }
      break;

    case "phone":
      if (!form.phone) {
        errors.phone = "Phone number is required";
      } else if (!validatePhone(form.phone)) {
        errors.phone = "Invalid phone number format";
      }
      break;

    case "email":
      if (!form.email) {
        errors.email = "Email is required";
      } else if (!validateEmail(form.email)) {
        errors.email = "Invalid email format";
      }
      break;

    case "currency":
      if (!form.currency) {
        errors.currency = "Currency is required";
      }
      break;
  }

  return !errors[fieldName];
};

// Funciones de validación para el formulario bancario
const validateBankField = (fieldName: keyof typeof bankForm) => {
  bankErrors[fieldName] = "";

  // Validaciones específicas por campo
  switch (fieldName) {
    case "bankName":
      if (!bankForm.bankName) {
        bankErrors.bankName = "Bank name is required";
      }
      break;

    case "bankAddress":
      if (!bankForm.bankAddress) {
        bankErrors.bankAddress = "Bank address is required";
      }
      break;

    case "swiftCode":
      if (!bankForm.swiftCode) {
        bankErrors.swiftCode = "Swift code is required";
      } else if (!validateSwiftCode(bankForm.swiftCode)) {
        bankErrors.swiftCode = "Invalid Swift code format";
      }
      break;

    case "ibanNumber":
      if (bankForm.ibanNumber && !validateIBAN(bankForm.ibanNumber)) {
        bankErrors.ibanNumber = "Invalid IBAN number format";
      }
      break;

    case "accountNumber":
      if (!bankForm.accountNumber) {
        bankErrors.accountNumber = "Account number is required";
      } else if (bankForm.accountNumber.length < 5) {
        bankErrors.accountNumber =
          "Account number must be at least 5 characters long";
      }
      break;

    case "beneficiaryName":
      if (!bankForm.beneficiaryName) {
        bankErrors.beneficiaryName = "Beneficiary name is required";
      }
      break;

    case "beneficiaryAddress":
      if (!bankForm.beneficiaryAddress) {
        bankErrors.beneficiaryAddress = "Beneficiary address is required";
      }
      break;
  }

  return !bankErrors[fieldName];
};

// Funciones de validación para el formulario de fabricas - actualizada para manejar múltiples fábricas
const validateFactoryField = (
  fieldName: keyof FactoryFormType,
  factoryIndex: number
) => {
  const factory = factoryForms.value[factoryIndex];
  if (!factory) return false;

  factory.errors[fieldName as keyof typeof factory.errors] = "";

  //Validación especificas por campo
  switch (fieldName) {
    case "factoryName":
      if (!factory.factoryName) {
        factory.errors.factoryName = "Factory name is required";
      } else if (factory.factoryName.length < 3) {
        factory.errors.factoryName =
          "Factory name must be at least 3 characters long";
      }
      break;

    case "factoryAddress":
      if (!factory.factoryAddress) {
        factory.errors.factoryAddress = "Address is required";
      } else if (factory.factoryAddress.length < 5) {
        factory.errors.factoryAddress =
          "Address must be at least 5 characters long";
      }
      break;

    case "factoryCountry":
      if (!factory.factoryCountry) {
        factory.errors.factoryCountry = "Country is required";
      }
      break;

    case "factoryProvince":
      if (!factory.factoryProvince) {
        factory.errors.factoryProvince = "County/province is required";
      }
      break;

    case "factoryCity":
      if (!factory.factoryCity) {
        factory.errors.factoryCity = "City is required";
      }
      break;

    case "factoryState":
      if (!factory.factoryState) {
        factory.errors.factoryState = "State is required";
      }
      break;

    case "factoryZipCode":
      if (!factory.factoryZipCode) {
        factory.errors.factoryZipCode = "Zip code is required";
      }
      break;

    case "factoryPhone":
      if (!factory.factoryPhone) {
        factory.errors.factoryPhone = "Phone number is required";
      } else if (!validatePhone(factory.factoryPhone)) {
        factory.errors.factoryPhone = "Invalid phone number format";
      }
      break;

    case "exportLicenseCode":
      if (!factory.exportLicenseCode) {
        factory.errors.exportLicenseCode = "Export license code is required";
      } else if (!validateExportLicenseCode(factory.exportLicenseCode)) {
        factory.errors.exportLicenseCode = "Invalid export license code format";
      }
      break;
  }

  return !factory.errors[fieldName as keyof typeof factory.errors];
};

// Función para validar todos los campos del formulario de proveedor
const validateForm = () => {
  let isValid = true;

  // Valida cada campo del formulario
  for (const field in form) {
    const isFieldValid = validateSupplierField(field as keyof typeof form);
    if (!isFieldValid) isValid = false;
  }

  return isValid;
};

// Función para validar todos los campos del formulario bancario
const validateBankForm = () => {
  let isValid = true;

  // Valida cada campo del formulario bancario
  for (const field in bankForm) {
    const isFieldValid = validateBankField(field as keyof typeof bankForm);
    if (!isFieldValid) isValid = false;
  }

  return isValid;
};

// Función para validar todos los formularios de fábrica
const validateFactoryForms = () => {
  let isValid = true;

  // Para cada formulario de fábrica
  factoryForms.value.forEach((factory, index) => {
    // Validar cada campo
    for (const field in factory) {
      if (field !== "errors") {
        // Evitar iterar sobre el objeto de errores
        const isFieldValid = validateFactoryField(
          field as keyof FactoryFormType,
          index
        );
        if (!isFieldValid) isValid = false;
      }
    }
  });

  return isValid;
};

// Funciones auxiliares de validación
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validatePhone = (phone: string): boolean => {
  // Acepta formatos como: +**********, ************, (*************
  const phoneRegex =
    /^(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}$/;
  return phoneRegex.test(phone);
};

const validateChineseSocialCreditCode = (code: string): boolean => {
  const regex = /^[1-9Y]{1}[1239]{1}[0-9]{6}[0-9A-Z]{9}[0-9X]$/;
  return regex.test(code);
};

const validateIndianCIN = (cin: string): boolean => {
  const regex = /^[UL][0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}$/;
  return regex.test(cin);
};

const validateExportLicenseCode = (code: string): boolean => {
  // Suponemos que debe ser alfanumérico, entre 8 y 20 caracteres
  const regex = /^[A-Z0-9]{8,20}$/i;
  return regex.test(code);
};

const validateSwiftCode = (swift: string): boolean => {
  // Formato básico de código SWIFT: 8 o 11 caracteres alfanuméricos
  const swiftRegex = /^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/;
  return swiftRegex.test(swift);
};

const validateIBAN = (iban: string): boolean => {
  // Formato básico IBAN: 2 letras seguidas de números
  const ibanRegex = /^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$/;
  return ibanRegex.test(iban);
};

// Manejador unificado para enviar todos los formularios
const handleUnifiedSubmit = () => {
  // Validar todos los formularios
  const isSupplierFormValid = validateForm();
  const isBankFormValid = validateBankForm();
  const isFactoryFormsValid = validateFactoryForms();

  if (isSupplierFormValid && isBankFormValid && isFactoryFormsValid) {
    // Crear un objeto JSON combinado con toda la información
    const combinedData = {
      supplierInformation: {
        ...form,
        // Extraer solo el código de la moneda si está seleccionada
        currency: form.currency ? form.currency.code : null,
      },
      bankInformation: {
        ...bankForm,
      },
      factoryInformation: factoryForms.value.map((factory) => {
        // Crear un objeto limpio sin los errores
        const { errors, ...cleanFactoryData } = factory;
        return cleanFactoryData;
      }),
    };

    // Convertir a JSON string para enviar al servidor
    const jsonData = JSON.stringify(combinedData, null, 2);

    console.log("Combined data saved successfully:", jsonData);

    // Aquí se puede implementar el envío al servidor
    // Por ejemplo: axios.post('/api/suppliers', combinedData)

    // Mostrar mensaje de éxito
    formSaved.value = true;

    // Ocultar el mensaje después de 5 segundos
    setTimeout(() => {
      formSaved.value = false;
    }, 5000);
    console.log("Successful form validation");
  } else {
    console.log("Form validation failed");
    formSaved.value = false;
  }
};
</script>

<style scoped>
/* .form-company-profile {
  background-image: url(src/assets/images/background-casaideas-05.png);
} */

.form-company-profile-flex {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

.logo {
  height: 100px;
}

.form-container {
  display: flex;
  flex-flow: column;
  align-items: center;
  max-width: 1340px;
  min-width: 300px;
  gap: 5px;
  padding: 10px;
}

.row1 {
  display: flex;
  flex-flow: row wrap;
}

.column1 {
  display: flex;
  flex-direction: column;
}

.justify-form {
  width: 520px;
  justify-content: center;
  gap: 10px;
}
.justifyFormSupplier {
  max-width: 1340px;
  min-width: 300px;
  max-height: 1420px;
  min-height: 330px;
  justify-content: center;
  gap: 10px;
}

.justifyFormBank {
  max-width: 1340px;
  min-width: 300px;
  max-height: 730px;
  min-height: 250px;
  justify-content: center;
  gap: 10px;
}

.justifyFormFactory {
  max-width: 1340px;
  min-width: 300px;
  max-height: 1200px;
  min-height: 250px;
  justify-content: center;
  gap: 10px;
}

.containerSuppliers {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 1340px;
  min-width: 300px;
  max-height: 1420px;
  min-height: 330px;
}

.containerBank {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 1340px;
  min-width: 300px;
  max-height: 730px;
  min-height: 250px;
}

.containerFactory {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  max-width: 1340px;
  min-width: 300px;
  max-height: 1200px;
  min-height: 250px;
  margin-bottom: 20px;
}

.containerStyle {
  background-color: rgba(128, 128, 128, 0.284);
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  padding: 10px;
}

.inputsize {
  width: 250px;
}

.textarea {
  background-color: rgba(255, 255, 255, 0.685);
  width: 450px;
  height: 100px;
  border-radius: 5px;
}

.textarea-factory {
  background-color: rgba(255, 255, 255, 0.685);
  width: 300px;
  height: 100px;
  border-radius: 5px;
}

.errorSize {
  height: 21px;
}

.factory-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 400px;
  margin: 20px 0;
}

.factory-count {
  font-weight: bold;
}

.success-message {
  background-color: #4caf50;
  color: white;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
  text-align: center;
}

.submit-container {
  margin: 20px 0;
}

.submitBtn {
  width: 180px;
}

.controls {
  display: flex;
  flex-direction: row;
  align-items: end;
  padding: 10px;
  gap: 10px;
}

.separateTitleForm {
  height: 30px;
}
</style>
