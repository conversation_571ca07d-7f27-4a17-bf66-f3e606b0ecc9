import axios from 'axios';
import type { Item } from '@/interfaces/ItemsInterface';

// URL base para todas las solicitudes
const BASE_URL = 'http://localhost:8089';

export const RelacionesService = {
  /**
   * Obtiene todos los proveedores disponibles
   * @returns Lista de todos los proveedores
   */
  async obtenerTodosProveedores(): Promise<Item[]> {
    try {
      // console.log("Intentando obtener todos los proveedores...");
      
      // Configurar un timeout más largo para esta solicitud específica
      const axiosConfig = {
        timeout: 60000, // 60 segundos
        signal: AbortSignal.timeout(60000) // Usar AbortSignal para mejor manejo de cancelación
      };
      
      const url = `${BASE_URL}/all-providers`;
      // console.log("URL completa:", url);
      
      // console.log("Iniciando solicitud HTTP con timeout extendido...");
      const response = await axios.get(url, axiosConfig);
      
      // console.log("Respuesta de proveedores:", response.data);
      return response.data || [];
    } catch (error: any) {
      if (error.code === 'ECONNABORTED') {
        // console.error("Error de timeout al obtener proveedores. El servidor está tardando demasiado en responder.");
      } else {
        // console.error("Error al obtener todos los proveedores:", error.message);
      }
      
      // Mostrar mensaje al usuario (opcional)
      // Swal.fire({
      //   title: 'Error de conexión',
      //   text: 'No se pudieron cargar los proveedores. El servidor está tardando demasiado en responder.',
      //   icon: 'error'
      // });
      
      return [];
    }
  },

  /**
   * Obtiene los proveedores relacionados con una madre
   * @param madreDescripcion descripcion de la madre
   * @returns Lista de proveedores relacionados
   */
  async obtenerProveedoresRelacionados(madreDescripcion: string): Promise<Item[]> {
    try {
      // console.log(`Buscando proveedores relacionados con madre: "${madreDescripcion}"`);
      
      const url = `${BASE_URL}/providers-by-mother`;
      // console.log("URL de la petición:", `${url}?madreDescripcion=${encodeURIComponent(madreDescripcion)}`);
      
      const response = await axios.get(`${url}?madreDescripcion=${encodeURIComponent(madreDescripcion)}`);
      
      // console.log("Respuesta completa:", response);
      // console.log("Datos recibidos:", {
      //   status: response.status,
      //   statusText: response.statusText,
      //   dataType: typeof response.data,
      //   isArray: Array.isArray(response.data),
      //   data: response.data
      // });
      
      // Si el backend devuelve un OK pero sin datos, podemos crear un array vacío
      if (response.status === 200 && (!response.data || (typeof response.data === 'object' && Object.keys(response.data).length === 0))) {
        // console.log("El backend devolvió OK pero sin datos. Devolviendo array vacío.");
        return [];
      }
      
      // Manejar diferentes formatos de respuesta
      if (Array.isArray(response.data)) {
        return response.data;
      } else if (response.data && typeof response.data === 'object') {
        // Verificar si hay una propiedad 'data' o similar que contenga los resultados
        const possibleDataProps = ['data', 'items', 'results', 'content', 'proveedores'];
        for (const prop of possibleDataProps) {
          if (response.data[prop] && Array.isArray(response.data[prop])) {
            // console.log(`Encontrados datos en response.data.${prop}`);
            return response.data[prop];
          }
        }
        
        // Si no hay arrays en propiedades conocidas, devolver el objeto en un array
        return [response.data];
      }
      
      return [];
    } catch (error) {
      console.error("Error al obtener proveedores relacionados:", error);
      return [];
    }
  },

  /**
   * Obtiene las madres relacionadas con un proveedor (creditor)
   * @param proveedorDescripcion descripción del proveedor
   * @returns Lista de madres relacionadas
   */
  async obtenerMadresRelacionadas(proveedorDescripcion: string): Promise<Item[]> {
    try {
      // console.log(`Buscando madres relacionadas con proveedor: "${proveedorDescripcion}"`);
      
      // Intentar con el nombre exacto del proveedor
      const url = `${BASE_URL}/mothers-by-creditor`;
      
      // Construir la URL con el parámetro correcto
      const fullUrl = `${url}?proveedorDescripcion=${encodeURIComponent(proveedorDescripcion)}`;
      // console.log("URL de la petición:", fullUrl);
      
      const response = await axios.get(fullUrl);
      // console.log("Respuesta de madres2:", response.data);
      
      // Manejar diferentes formatos de respuesta
      if (Array.isArray(response.data)) {
        return response.data;
      } else if (response.data && typeof response.data === 'object') {
        // Verificar si hay una propiedad 'data' o similar que contenga los resultados
        const possibleDataProps = ['data', 'items', 'results', 'content', 'madres'];
        for (const prop of possibleDataProps) {
          if (response.data[prop] && Array.isArray(response.data[prop])) {
            // console.log(`Encontrados datos en response.data.${prop}`);
            return response.data[prop];
          }
        }
        
        // Si no hay arrays en propiedades conocidas, devolver el objeto en un array
        return [response.data];
      }
      
      return [];
    } catch (error) {
      console.error("Error al obtener madres relacionadas:", error);
      return [];
    }
  }
};
