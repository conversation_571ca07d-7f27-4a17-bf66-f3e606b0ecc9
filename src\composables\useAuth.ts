// src/composables/useAuth.ts
import { jwtDecode } from 'jwt-decode';

export function useAuth() {
  const token = localStorage.getItem('access_token');
  let decoded: any = null;

  if (token) {
    decoded = jwtDecode(token);
  }

  const getEmail = () => decoded?.email || '';
  const getUsername = () => decoded?.preferred_username || '';
  const getRoles = () => decoded?.resource_access?.myclient?.roles || []

  const hasRole = (role: string) => getRoles().includes(role);

  return {
    token,
    decoded,
    getEmail,
    getUsername,
    getRoles,
    hasRole,
  };
}