export interface Proveedor {
  tipo?: "existente" | "nuevo";
  supplierId?: string;
  companyName?: string;
  companyOwnerName?: string;
  companyOwnerPhone?: string;
  companyOwnerEmail?: string;
  socialCreditCode?: string;
  exportLicenseCode?: string;
  address?: string;
  country?: string;
  province?: string;
  city?: string;
  state?: string;
  contactName?: string;
  phone?: string;
  email?: string;
  paymentsTerms?: string;
  currency?: string;
  bankName?: string;
  bankAddress?: string;
  swiftCode?: string;
  ibanNumber?: string;
  accountNumber?: string;
  beneficiaryName?: string;
  beneficiaryAddress?: string;
  companyProfileStatus?: string;
  correoPersonalizado?: PersonalizacionCorreo;
  status?: string;
  createdAt?: string;
}

export interface PersonalizacionCorreo {
  subject: string;
  body: string;
  cc: string[];
}
