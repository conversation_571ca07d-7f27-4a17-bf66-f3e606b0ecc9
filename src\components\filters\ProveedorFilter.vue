<template>
  <form
    @submit.prevent="emitSearch"
    class="flex flex-wrap gap-4 items-end bg-gray-100 p-4 rounded shadow"
  >
    <div class="flex flex-col">
      <label for="id" class="text-sm font-semibold">Proveedor ID</label>
      <input
        id="id"
        v-model="filters.id"
        type="text"
        class="form-input"
        placeholder="Ej: PRO12345"
      />
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-semibold">Fecha Desde</label>
      <input type="date" v-model="filters.fechaDesde" class="form-input" />
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-semibold">Fecha <PERSON></label>
      <input type="date" v-model="filters.fechaHasta" class="form-input" />
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-semibold">Company Name</label>
      <input
        v-model="filters.companyName"
        type="text"
        class="form-input"
        placeholder="Nombre de empresa"
      />
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-semibold">País</label>
      <input
        v-model="filters.country"
        type="text"
        class="form-input"
        placeholder="Ej: China"
      />
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-semibold">Email</label>
      <input
        v-model="filters.email"
        type="email"
        class="form-input"
        placeholder="<EMAIL>"
      />
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-semibold">Estado</label>
      <select v-model="filters.status" class="form-input">
        <option value="">Todos</option>
        <option value="Creado">Creado</option>
        <option value="ActProveedor">ActProveedor</option>
        <option value="ActSourcing">ActSourcing</option>
        <option value="Activo">Activo</option>
      </select>
    </div>

  </form>

  <!-- Botones centrados -->
  <div class="buttons-container">
    <button type="button" class="btn-action" @click="emitSearch">Buscar</button>
    <button type="button" class="btn-secondary" @click="limpiarFiltros">Limpiar</button>
  </div>
</template>

<script setup lang="ts">
import { reactive } from "vue";

const emit = defineEmits(["search"]);

const filters = reactive({
  id: "",
  fechaDesde: "",
  fechaHasta: "",
  companyName: "",
  country: "",
  email: "",
  status: "", // <- nuevo campo
});

function limpiarFiltros() {
  Object.keys(filters).forEach((k) => {
    filters[k as keyof typeof filters] = "";
  });
}

function emitSearch() {
  emit("search", { ...filters });
}
</script>

<style scoped>
form {
  background-color: #ffffff32;
  margin-bottom: 0.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.form-input {
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  padding: 0.5rem 0.5rem;
  font-size: 0.875rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
  outline: none;
}

/* Contenedor de botones centrados */
.buttons-container {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 0.5rem 0 1.5rem 0;
}

.btn-action {
  background-color: #2563eb;
  color: white;
  font-weight: 600;
  border-radius: 0.25rem;
  padding: 0.5rem 2rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 120px;
}

.btn-action:hover {
  background-color: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
  font-weight: 600;
  border-radius: 0.25rem;
  padding: 0.5rem 2rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 120px;
}

.btn-secondary:hover {
  background-color: #4b5563;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style>
