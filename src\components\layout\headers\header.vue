<template>
  <header class="header-box d-flex justify-content-between align-items-center mb-4 px-4 py-1 rounded animate-fade">
    <div class="d-flex align-items-center">
      <i v-if="icon" :class="`bi bi-${icon} display-3 me-2 text-secondary `"></i>
      <h4 class="fw-bold text-secondary m-0">{{ title }}</h4>
    </div>

    <div class="">
      <router-link to="/enviocorreo" class="btn-logout">
        <span class="material-icons">mail</span>
      </router-link>
    </div>

    <div class="logout-container" v-if="isHomePage">
      <button class="btn btn-logout" @click="$emit('logout')">
        <i class="bi bi-box-arrow-right me-2"></i> Cerrar sesión
      </button>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const isHomePage = computed(() => route.path === '/')



defineProps<{
  title: string
  icon?: string
}>()
</script>

<style scoped>
.header-box {
  background-color: rgba(255, 255, 255, 0.197);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-radius: 12px;
}

.logout-container {
  display: flex;
  align-items: center;
}

.btn-logout {
  background: linear-gradient(144deg, var(--yellow-200), var(--yellow-400), var(--orange-300));
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-logout:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background: linear-gradient(144deg, var(--yellow-200), var(--yellow-400), var(--orange-300));
}


.material-icons {
  font-size: 2rem;
  color: gray;
  transition: 0.2s ease-out;
  
  &:hover {
    .material-icons {
      color: black;
      transform: translateX(0.5rem);
    }
  }
}
  
</style>
