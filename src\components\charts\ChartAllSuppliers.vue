<template>
  <div>
    <div class="mb-3">
      <label class="font-bold mr-2">Selecciona una fecha:</label>
      <Calendar v-model="selectedDate" dateFormat="dd/mm/yy" showIcon />
    </div>

    <!-- <PERSON><PERSON> de <PERSON> (Semana / Mes / Año) -->
    <div class="mb-4">
      <label class="font-bold mr-2">Agrupar por:</label>
      <SelectButton
        v-model="selectedFilter"
        :options="['Semana', 'Mes', 'Año']" />
    </div>

    <Chart type="bar" :data="chartData" :options="chartOptions" />
  </div>
</template>

<script setup lang="ts">
import Chart from "primevue/chart";
import { ref, computed } from "vue";
import Calendar from "primevue/calendar";
import SelectButton from "primevue/selectbutton";
import {
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
} from "date-fns";

interface DataEntry {
  date: string;
  createdsupliers: number;
  requestedsupliers: number;
  editedsuppliers: number;
}

const allData: DataEntry[] = [
  {
    date: "2025-01-01",
    createdsupliers: 1,
    requestedsupliers: 2,
    editedsuppliers: 1,
  },
  {
    date: "2025-01-05",
    createdsupliers: 1,
    requestedsupliers: 5,
    editedsuppliers: 1,
  },
  {
    date: "2025-01-10",
    createdsupliers: 2,
    requestedsupliers: 1,
    editedsuppliers: 3,
  },
  {
    date: "2025-01-15",
    createdsupliers: 5,
    requestedsupliers: 0,
    editedsuppliers: 0,
  },
  {
    date: "2025-01-20",
    createdsupliers: 0,
    requestedsupliers: 0,
    editedsuppliers: 0,
  },
  {
    date: "2025-01-25",
    createdsupliers: 1,
    requestedsupliers: 1,
    editedsuppliers: 2,
  },
  {
    date: "2025-02-01",
    createdsupliers: 2,
    requestedsupliers: 5,
    editedsuppliers: 1,
  },
  {
    date: "2025-02-05",
    createdsupliers: 1,
    requestedsupliers: 3,
    editedsuppliers: 1,
  },
  {
    date: "2025-02-10",
    createdsupliers: 1,
    requestedsupliers: 1,
    editedsuppliers: 5,
  },
  {
    date: "2025-02-15",
    createdsupliers: 3,
    requestedsupliers: 1,
    editedsuppliers: 3,
  },
  {
    date: "2025-02-20",
    createdsupliers: 3,
    requestedsupliers: 1,
    editedsuppliers: 1,
  },
  {
    date: "2025-02-25",
    createdsupliers: 6,
    requestedsupliers: 1,
    editedsuppliers: 6,
  },
];

// Tipado de las variables reactivas
const selectedDate = ref<Date>(new Date());
const selectedFilter = ref<"Semana" | "Mes" | "Año">("Año");

// Computed para filtrar los datos según el período seleccionado
const filteredData = computed<DataEntry[]>(() => {
  const selected = selectedDate.value;
  let startDate: Date, endDate: Date;

  switch (selectedFilter.value) {
    case "Semana":
      startDate = startOfWeek(selected, { weekStartsOn: 1 });
      endDate = endOfWeek(selected, { weekStartsOn: 1 });
      break;
    case "Mes":
      startDate = startOfMonth(selected);
      endDate = endOfMonth(selected);
      break;
    case "Año":
      startDate = startOfYear(selected);
      endDate = endOfYear(selected);
      break;
  }

  return allData.filter((d) => {
    const dataDate = new Date(d.date);
    return dataDate >= startDate! && dataDate <= endDate!;
  });
});

// Computed para generar los datos del gráfico
const chartData = computed(() => {
  const labels = filteredData.value.map((d) => d.date);
  const createdSuppliers = filteredData.value.map((d) => d.createdsupliers);
  const requestedSuppliers = filteredData.value.map((d) => d.requestedsupliers);
  const editedSuppliers = filteredData.value.map((d) => d.editedsuppliers);

  return {
    labels,
    datasets: [
      {
        label: "Created Suppliers",
        backgroundColor: "#42A5F5",
        data: createdSuppliers,
      },
      {
        label: "Resquested Suppliers",
        backgroundColor: "#FFA726",
        data: requestedSuppliers,
      },
      {
        label: "Edited Suppliers",
        backgroundColor: "#A020F0",
        data: editedSuppliers,
      },
    ],
  };
});

// Opciones del gráfico tipadas
const chartOptions = ref<Record<string, unknown>>({
  responsive: true,
  plugins: { legend: { position: "top" } },
});
</script>
