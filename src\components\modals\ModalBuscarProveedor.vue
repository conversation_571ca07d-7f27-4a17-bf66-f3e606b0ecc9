<template>
  <div
    v-if="visible"
    class="fixed inset-0 bg-black/40 backdrop-blur-sm flex items-center justify-center px-4 animate-fade animate-duration-[300ms] animate-delay-100 animate-ease-in"
  >
    <div
      class="bg-white/80 contenedor rounded-xl shadow-xl overflow-y-auto p-4"
    >
      <!-- Título -->
      <div class="d-flex header align-items-center mb-4">
        <i class="pi pi-address-book me-2"></i>
        <h1 class="fw-bold m-0">Gestión de Proveedores y Materiales</h1>
        <button class="btn-close ms-auto" @click="$emit('close')"></button>
      </div>

      <!-- Barra de navegación principal -->
      <div class="nav-buttons-container mb-4">
        <button
          class="nav-button"
          :class="{ active: activeSection === 'buscar' }"
          @click="activeSection = 'buscar'"
        >
          <i class="pi pi-search"></i>
          <span>Buscar Datos</span>
        </button>

        <button
          class="nav-button"
          :class="{ active: activeSection === 'agregar' }"
          @click="activeSection = 'agregar'"
        >
          <i class="pi pi-plus-circle"></i>
          <span>Agregar Manualmente</span>
        </button>

        <button
          class="nav-button"
          :class="{ active: activeSection === 'resumen' }"
          @click="activeSection = 'resumen'"
        >
          <i class="pi pi-list"></i>
          <span>Resumen Cotización</span>
          <span class="badge" v-if="totalProveedores > 0">{{
            totalProveedores
          }}</span>
        </button>
      </div>

      <!-- Contenido principal que cambia según la sección activa -->
      <div class="content-container">
        <!-- SECCIÓN 1: BUSCAR PROVEEDORES/SKUS -->
        <BusquedaProveedoresSkus v-if="activeSection === 'buscar'" class="section-container" />

        <!-- SECCIÓN 2: AGREGAR MANUALMENTE -->
         <AgregarProvSkuManual v-if="activeSection === 'agregar'" class="section-container" />
        
        <!-- SECCIÓN 3: RESUMEN COTIZACIÓN -->
        <preResumenCotizacion v-if="activeSection === 'resumen'"  class="section-container"/>
      </div>
      <div class="d-flex justify-content-center btn-cerrar-modal">
        <button class="btn btn-outline-danger mt-3" @click="$emit('close')">
          Cerrar
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { useCotizacionStore } from "@/stores/cotizacionStore";
import BusquedaProveedoresSkus from "@/components/busqueda/BusquedaProveedoresSkus.vue";
import AgregarProvSkuManual from "@/components/agregarManual/AgregarProvSkuManual.vue";
import preResumenCotizacion from "@/components/resumenCotizacion/preResumenCotizacion.vue";
import "primeicons/primeicons.css";

// Variables para la navegación entre secciones
const activeSection = ref("buscar"); // Valores posibles: 'buscar', 'agregar', 'resumen'

const props = defineProps<{
  visible: boolean;
}>();

const store = useCotizacionStore()

// Computed properties
const totalProveedores = computed(() => store.proveedores.length);
const emit = defineEmits(["close"]);

watch(
  () => store.cotizacionActual,
  (cotiz) => {
    console.log("🧾 Cotización armada hasta ahora:", cotiz);
  },
  { deep: true }
);
</script>

<style scoped>
.fixed {
  overflow: auto;
}

.contenedor {
  min-height: 90vh; /* ocupa al menos el 70% del alto del viewport */
  max-height: 90vh; /* nunca sobrepasa el 90% */
  height: auto; /* permite crecer si es necesario */
  overflow-y: auto; /* scroll interno si hay mucho contenido */
  width: 100%;
  max-width: 1800px; /* o 90%, o lo que estimes */
  margin: 0 auto; /* centra horizontalmente */
}

.header {
  border: 2px solid #ffffff46;
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: #ffffff20;
}

.header:hover {
  border-color: var(--secondary);
}

/* Estilos para los íconos */
.pi-search {
  font-size: 2rem;
}

.pi-address-book {
  font-size: 3rem;
  margin-right: 1rem;
}

/* Estilos para los botones de navegación */
.nav-buttons-container {
  position: sticky;
  top: 0;
  z-index: 50; /* asegúrate que quede por encima de otros elementos */
  background-color: #e3e3e3; /* importante: el sticky necesita fondo */
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.nav-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  border: none;
  border-radius: 0.5rem;
  background-color: rgba(201, 200, 200, 0.797);
  color: #495057;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.nav-button i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.nav-button span {
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
}

.nav-button.active {
  background-color: #f8f9fa;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
  transform: translateY(2px);
}

.nav-button:hover:not(.active) {
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.1);
}

.nav-button:nth-child(1) {
  border-bottom: 3px solid var(--primary);
}

.nav-button:nth-child(2) {
  border-bottom: 3px solid var(--alt-2);
}

.nav-button:nth-child(3) {
  border-bottom: 3px solid var(--light);
}

.badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: #ff6a88;
  color: white;
  border-radius: 50%;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
}

.content-container{
  min-height: 55vh;
}
</style>