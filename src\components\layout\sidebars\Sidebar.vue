<template>
  <aside :class="`${is_expanded ? 'is-expanded' : ''}`">
    <div class="tittle-header">
      <div class="logo">
        <img :src="logoURL" alt="Casaideas-Icono" />
      </div>
      <div class="titulo" v-if="is_expanded">
        <img :src="casaIdea" alt="Casaideas" />
      </div>
    </div>

    <div class="menu-toggle-wrap">
      <button class="menu-toggle" @click="ToggleMenu">
        <span class="material-icons">keyboard_double_arrow_right</span>
      </button>
    </div>

    <h3>Menu</h3>
    <div class="menu">
      <router-link to="/" class="button">
        <span class="material-icons">home</span>
        <span class="text">Inicio</span>
      </router-link>
      <router-link v-if="hasRole('Diseño') || hasRole('Sourcing') || hasRole('Auditor')" to="/cotizaciones" class="button">
        <span class="material-icons">description</span>
        <span class="text">Cotizaciones</span>
      </router-link>
      <router-link v-if="hasRole('Sourcing') || hasRole('Auditor')" to="/proveedores" class="button">
        <span class="material-icons">group</span>
        <span class="text">Proveedores</span>
      </router-link>
      <router-link v-if="hasRole('Auditor')" to="/auditoria" class="button">
        <span class="material-icons">view_timeline</span>
        <span class="text">Auditoria</span>
      </router-link>
      <router-link v-if="hasRole('Sourcing') || hasRole('Auditor')"to="/report" class="button">
        <span class="material-icons">show_chart</span>
        <span class="text">Reportes</span>
      </router-link>
      <router-link v-if="hasRole('Diseño') || hasRole('Sourcing') || hasRole('Auditor')" to="/enviocorreo" class="button">
        <span class="material-icons">mail</span>
        <span class="text">Correo</span>
      </router-link>
    </div>

    <div class="flex"></div>

    <div class="menu">
      <button class="button w-100" @click="logout">
        <span class="material-icons">logout</span>
        <span class="text">Logout</span>
      </button>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import logoURL from "@/assets/images/homeIdeas.png";
import casaIdea from "@/assets/images/logoTipo.png";
import keycloak from '@/auth/keycloak';
import { useAuth } from "@/composables/useAuth";
import { jwtDecode } from "jwt-decode";


const {  hasRole } = useAuth();
const token = keycloak.token;
if (token) {
  const decoded: any = jwtDecode(token);
  console.log("🧾 Token Decodificado:", decoded);
  console.log("Has roles:", decoded.resource_access?.myclient?.roles);

}

function logout() {
  keycloak.logout({
    redirectUri: window.location.origin
  });
}
// Definir el estado de la expansión del menú
const is_expanded = ref<boolean>(false);

// Función para alternar manualmente
const ToggleMenu = (): void => {
  is_expanded.value = !is_expanded.value;
  localStorage.setItem("is_expanded", String(is_expanded.value));
};

// Detectar cambios en el ancho de pantalla en tiempo real
const checkScreenSize = () => {
  if (window.innerWidth > 1025) {
    is_expanded.value = true;
  } else {
    is_expanded.value = false;
  }
};

// Agregamos el listener para detectar cambios de tamaño de pantalla
onMounted(() => {
  // Esperar un poco para animar expansión
  setTimeout(() => {
    checkScreenSize(); // Se expande si la pantalla es grande
  }, 500); // Delay de animación (puede ser más o menos)

  window.addEventListener("resize", checkScreenSize);
});

// Limpiamos el listener cuando el componente se destruye
onUnmounted(() => {
  window.removeEventListener("resize", checkScreenSize);
});
</script>

<style lang="scss" scoped>
button {
  cursor: pointer;
  appearance: none;
  border: none;
  outline: none;
  background: none;
}

aside {
  display: flex;
  flex-direction: column;

  background-color: var(--dark);

  width: calc(2rem + 30px);
  overflow: hidden;
  padding: 1rem;

  transition: 0.4s ease-in-out;

  .flex {
    flex: 1 1 0%;
  }

  .logo {
    margin-bottom: 1rem;
    img {
      width: 3rem;
    }
  }
  .titulo {
    img {
      width: 9rem;
    }
  }

  .menu-toggle-wrap {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 1rem;
    position: relative;
    top: 0;
    transition: 0.2s ease-in-out;

    .menu-toggle {
      transition: 0.2s ease-in-out;
      .material-icons {
        font-size: 2rem;
        color: var(--secondary);
        transition: 0.2s ease-out;
      }

      &:hover {
        .material-icons {
          color: var(--dark-alt);
          transform: translateX(0.5rem);
        }
      }
    }
  }

  h3,
  .button .text {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }

  h3 {
    color: var(--light-alt);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
  }

  .menu {
    margin: 0 -1rem;

    .button {
      display: flex;
      align-items: center;
      text-decoration: none;

      transition: 0.2s ease-in-out;
      padding: 0.5rem 1rem;

      .material-icons {
        font-size: 2rem;
        color: var(--light);
        transition: 0.2s ease-in-out;
      }
      .text {
        color: var(--light-alt);
        transition: 0.2s ease-in-out;
      }

      &:hover {
        background-color: var(--secondary);

        .material-icons,
        .text {
          color: var(--primary);
        }
      }

      &.router-link-exact-active {
        background-color: var(--secondary);
        border-right: 5px solid var(--primary);

        .material-icons,
        .text {
          color: var(--primary);
        }
      }
    }
  }

  &.is-expanded {
    width: var(--sidebar-width);
    min-width: var(--sidebar-width);
    
    .menu-toggle-wrap {
      top: -3rem;

      .menu-toggle {
        transform: rotate(-180deg);
      }
    }

    h3,
    .button .text {
      opacity: 1;
    }

    .button {
      .material-icons {
        margin-right: 1rem;
      }
    }

    .footer {
      opacity: 0;
    }
  }
}

@media (max-width: 412px) {
  aside {
    width: calc(4.5rem);
  }
  .text {
    font-size: 1vh;
  }
  .menu-toggle-wrap {
    margin-top: 2.2rem;
  }
}
</style>
