<template>
  <div>
    <Dialog
      class="animate-fade-down animate-duration-500 animate-normal"
      v-model:visible="Boolean"
      :modal="false"
      :header="'Suppliers History'"
      :style="{ width: '60vw', heigth: '60vw' }"
      :closable="false">
      <ChartAllSuppliers
        class="animate-fade-down animate-duration-500 animate-delay-500 animate-normal" />

      <template #footer>
        <Button
          @click="$emit('closeSuppliers')"
          label="Close"
          icon="pi pi-times"
          class="p-button-text" />
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import Dialog from "primevue/dialog";
import But<PERSON> from "primevue/button";
import ChartAllSuppliers from "../charts/ChartAllSuppliers.vue";

defineProps({
  visible: Boolean,
});

const emit = defineEmits(["closeSuppliers"]);
</script>
