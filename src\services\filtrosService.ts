import axios from "axios";
import Swal from 'sweetalert2';

// Constantes para configuración
const API_BASE_URL = "http://localhost:8089"; // Verifica que esta URL sea correcta
const DEFAULT_TIMEOUT = 30000; // 30 segundos
const MAX_RESULTS = 500; // Límite máximo de resultados a procesar
const DEBUG = true; // Activar/desactivar logs de depuración

// Función auxiliar para logging
const log = {
  debug: (...args: any[]) => DEBUG && console.log(...args),
  info: (...args: any[]) => console.log(...args),
  warn: (...args: any[]) => console.warn(...args),
  error: (...args: any[]) => console.error(...args)
};

// Configuración global de Axios
axios.defaults.timeout = DEFAULT_TIMEOUT;
axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.headers.common['Accept'] = 'application/json';

export const FiltroService = {
  async obtenerFiltros() {
    const response = await axios.get(
      "http://localhost:8080/api/dropdown-opciones"
    );
    console.log("Filtros obtenidos: en buscar por", response.data);//nombres de los filtros
    return response.data;
  },

  async realizarBusqueda(filtrosSeleccionados: Record<string, string>) {
    try {
      // log.debug("Enviando filtros al backend:", JSON.stringify(filtrosSeleccionados, null, 2));
      
      // Verificar si hay filtros para enviar
      if (!filtrosSeleccionados || Object.keys(filtrosSeleccionados).length === 0) {
        log.warn("No hay filtros para enviar al backend");
        throw new Error("No hay filtros seleccionados");
      }
      
      // Corregir posibles errores en los filtros
      const filtrosCorregidos = this.corregirFiltros(filtrosSeleccionados);
      // log.debug("Filtros corregidos:", JSON.stringify(filtrosCorregidos, null, 2));
      
      // Añadir parámetro de límite si el backend lo soporta
      if (!filtrosCorregidos.limit) {
        filtrosCorregidos.limit = MAX_RESULTS.toString();
      }
      
      // Intentar conectar directamente sin verificar el servidor primero
      // log.debug("URL de la petición:", `${API_BASE_URL}/articles/filter`);
      
      // Mostrar mensaje de carga
      Swal.fire({
        title: 'Buscando...',
        text: 'Esto puede tomar unos momentos',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        }
      });
      
      // Realizar la petición al backend
      const response = await axios.post(
        "http://localhost:8089/articles/filter",
        filtrosSeleccionados
      );

      // console.log("Respuesta del backend:", response);

      // Verificar si la respuesta tiene la estructura esperada
      if (response && response.data) {
        // console.log("Estructura de la respuesta:", 
        //   {
        //   status: response.status,
        //   statusText: response.statusText,
        //   dataType: typeof response.data,
        //   isArray: Array.isArray(response.data),
        //   dataLength: Array.isArray(response.data) ? response.data.length : 'N/A'
        //   });

        // Si la respuesta no es un array, intentar convertirla
        if (!Array.isArray(response.data) && typeof response.data === 'object') {
          // console.warn("La respuesta no es un array, intentando convertir...");

          // Si la respuesta tiene una propiedad que contiene los datos
          const possibleDataProperties = ['data', 'items', 'results', 'content'];
          for (const prop of possibleDataProperties) {
            if (response.data[prop] && Array.isArray(response.data[prop])) {
              // console.log(`Encontrados datos en response.data.${prop}`);
              response.data = response.data[prop];
              break;
            }
          }
        }
      }

      return response;
    } catch (error) {
      // console.error("Error en la petición al backend:", error);
      throw error;
    }
  },
  
  // Método para procesar la respuesta del servidor
  procesarRespuesta(data: any): any[] {
    // Si no hay datos, devolver array vacío
    if (!data) {
      // log.warn("No se recibieron datos del servidor");
      return [];
    }
    
    // Si ya es un array, devolverlo directamente
    if (Array.isArray(data)) {
      // log.debug(`Datos recibidos como array (${data.length} elementos)`);
      return data;
    }
    
    // Si es un objeto, buscar propiedades que contengan arrays
    if (typeof data === 'object') {
      // log.warn("La respuesta no es un array, intentando convertir...");
      
      // Buscar propiedades que contengan arrays
      const possibleDataProperties = ['data', 'items', 'results', 'content', 'articles'];
      for (const prop of possibleDataProperties) {
        if (data[prop] && Array.isArray(data[prop])) {
          // log.debug(`Encontrados datos en data.${prop} (${data[prop].length} elementos)`);
          return data[prop];
        }
      }
      
      // Si no se encontró ninguna propiedad con array, convertir el objeto a array
      // log.warn("No se encontró ninguna propiedad con array, convirtiendo objeto a array");
      return [data];
    }
    
    // Si no es ni array ni objeto, devolver array vacío
    // log.error(`Tipo de datos no reconocido: ${typeof data}`);
    return [];
  },
  
  // Método para corregir posibles errores en los filtros
  corregirFiltros(filtros: Record<string, string>): Record<string, string> {
    const filtrosCorregidos: Record<string, string> = {};
    
    // Mapeo correcto de campos
    Object.entries(filtros).forEach(([campo, valor]) => {
      // Si el campo es madreId pero el valor parece una descripción
      if (campo === 'madreId' && valor && valor.length > 10 && !/^\d+$/.test(valor)) {
        // console.log(`El valor de madreId parece ser una descripción: "${valor}"`);
        // Mover el valor a madreDescripcion
        filtrosCorregidos['madreDescripcion'] = valor;
      } 
      // Si el campo es proveedorCod pero el valor parece una descripción (contiene espacios o caracteres especiales)
      else if (campo === 'proveedorCod' && valor && /[^a-zA-Z0-9-]/.test(valor)) {
        // console.log(`El valor de proveedorCod parece ser una descripción: "${valor}"`);
        // Mover el valor a proveedorDescripcion
        filtrosCorregidos['proveedorDescripcion'] = valor;
      }
      else if (valor) { // Solo incluir campos con valor
        // Mantener el campo original
        filtrosCorregidos[campo] = valor;
      }
    });
    
    return filtrosCorregidos;
  },
  
  // Método para obtener madres por proveedor
  async obtenerMadresPorProveedor(proveedorDescripcion: string) {
    try {
      console.log('obtenerMadresPorProveedor: ', proveedorDescripcion);
      
      const axiosConfig = {
        timeout: DEFAULT_TIMEOUT,
        headers: {
          'Content-Type': 'application/json'
        }
      };
      
      const url = `${API_BASE_URL}/mothers-by-creditor?proveedorDescripcion=${encodeURIComponent(proveedorDescripcion)}`;
      
      // log.debug(`URL de petición: ${url}`);
      
      const response = await axios.get(url, axiosConfig);
      
      log.debug("Respuesta completa:", response);
      log.debug("Madres obtenidas:", {
        dataType: typeof response.data,
        isArray: Array.isArray(response.data),
        length: Array.isArray(response.data) ? response.data.length : 'N/A',
        data: response.data
      });
      
      // Manejar diferentes formatos de respuesta
      if (Array.isArray(response.data)) {
        return response.data;
      } else if (response.data && typeof response.data === 'object') {
        // Buscar si hay alguna propiedad que contenga un array
        const arrayProps = Object.keys(response.data).find(key => 
          Array.isArray(response.data[key])
        );
        
        if (arrayProps) {
          return response.data[arrayProps];
        }
        
        // Si no hay arrays, devolver el objeto en un array
        return [response.data];
      }
      
      return [];
    } catch (error) {
      console.error('Error al obtener madres para el proveedor', proveedorDescripcion, error);
      // Devolver un array vacío en caso de error para evitar que la aplicación falle
      return [];
    }
  }
  
}

