<template>
  <Header title="Proveedores" icon="bi bi-file-person" />

  <main class="animate-fade animate-once animate-duration-[2000ms] animate-delay-500">
    <div class="cotizaciones-view p-4">
      <!-- Filtros -->
      <ProveedorFilter @search="aplicarFiltros" />

      <!-- Mensaje cuando no hay resultados -->
      <div v-if="proveedoresFiltrados.length === 0" class="alert alert-info text-center my-4">
        No se encontraron proveedores que coincidan con los filtros aplicados.
        <div class="mt-3">
          <button class="btn btn-primary" @click="abrirModalDemostracion">
            <i class="bi bi-eye"></i> Ver Demostración del Modal
          </button>
        </div>
      </div>

      <!-- Tabla de Proveedores -->
      <ProveedoresTable
        v-else
        :proveedores="proveedoresFiltrados"
        @excluir="excluirProveedor"
        @cambiarEstado="cambiarEstadoProveedor"
        @verDetalles="abrirDetallesProveedor"
      />

      <!-- El modal de detalles ha sido reemplazado por un Swal -->
    </div>
  </main>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type { Proveedor } from "@/interfaces/ProveedorInterface";
import { mockProveedores } from "@/mocks/mockProveedores";
import ProveedorFilter from "@/components/filters/ProveedorFilter.vue";
import ProveedoresTable from "@/components/tables/ProveedoresTable.vue";
import Header from "@/components/layout/headers/header.vue";
import Swal from "sweetalert2";
import keycloak from "@/auth/keycloak";
import { useAuth } from "@/composables/useAuth";
import { jwtDecode } from "jwt-decode";
import { useRouter } from "vue-router";

// validación de permiso de acceso a ruta según role
const { hasRole } = useAuth();
const token = keycloak.token;
if (token) {
  const decoded: any = jwtDecode(token);
  console.log("🧾 Token Decodificado:", decoded);
  console.log("Has roles:", decoded.resource_access?.myclient?.roles);
}

const allowedRoles = ["Sourcing", "Auditor"];

onMounted(() => {
  const isAuthorized = allowedRoles.some((role) => hasRole(role));

  if (!isAuthorized) {
    router.push({ name: "Home" });
  }
});

const router = useRouter()


// Estado de los proveedores
const proveedores = ref<Proveedor[]>(mockProveedores);
const proveedoresFiltrados = ref<Proveedor[]>([]);

// Ya no necesitamos estado para el modal de detalles porque usamos Swal

// Cargar todos los proveedores al iniciar
onMounted(() => {
  proveedoresFiltrados.value = [...proveedores.value];
});

function aplicarFiltros(filtros: any) {
  proveedoresFiltrados.value = proveedores.value.filter((p) => {
    const coincideID = filtros.id
      ? p.supplierId?.toLowerCase().includes(filtros.id.toLowerCase())
      : true;

    const coincideCompany = filtros.companyName
      ? p.companyName?.toLowerCase().includes(filtros.companyName.toLowerCase())
      : true;

    const coincideCountry = filtros.country
      ? p.country?.toLowerCase().includes(filtros.country.toLowerCase())
      : true;

    const coincideEmail = filtros.email
      ? p.email?.toLowerCase().includes(filtros.email.toLowerCase())
      : true;

    const coincideStatus = filtros.status
      ? p.status === filtros.status
      : true;

    const fechaCreacion = new Date(p.createdAt || "");
    const desde = filtros.fechaDesde ? new Date(filtros.fechaDesde) : null;
    const hasta = filtros.fechaHasta ? new Date(filtros.fechaHasta) : null;

    const coincideFecha =
      (!desde || fechaCreacion >= desde) && (!hasta || fechaCreacion <= hasta);

    return (
      coincideID &&
      coincideCompany &&
      coincideCountry &&
      coincideEmail &&
      coincideStatus &&
      coincideFecha
    );
  });
}

function excluirProveedor(proveedor: Proveedor) {
  Swal.fire({
    title: '¿Estás seguro?',
    text: `¿Realmente deseas eliminar al proveedor ${proveedor.companyName || proveedor.email}?`,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Sí, eliminar',
    cancelButtonText: 'Cancelar'
  }).then((result) => {
    if (result.isConfirmed) {
      proveedores.value = proveedores.value.filter((p) => p !== proveedor);
      proveedoresFiltrados.value = proveedoresFiltrados.value.filter((p) => p !== proveedor);

      Swal.fire(
        'Eliminado',
        'El proveedor ha sido eliminado correctamente.',
        'success'
      );
    }
  });
}

// Esta función ya no abre un modal, sino que muestra un Swal directamente desde ProveedoresTable.vue
function abrirDetallesProveedor(_proveedor: Proveedor) {
  console.log('Esta función ya no se usa, los detalles se muestran directamente en un Swal');
  // No hacemos nada aquí porque los detalles se muestran directamente en ProveedoresTable.vue
}

// Función para mostrar un ejemplo de proveedor en el Swal
function abrirModalDemostracion() {
  console.log('Mostrando ejemplo de proveedor');
  // Usamos el primer proveedor del mock para la demostración
  const proveedorEjemplo = mockProveedores[0];

  // Formatear la fecha de creación
  const fechaCreacion = proveedorEjemplo.createdAt ? new Date(proveedorEjemplo.createdAt).toLocaleDateString() : 'No especificada';

  // Crear el contenido HTML para el Swal
  const contenidoHTML = `
    <div class="text-start">
      <div class="mb-4">
        <h5 class="border-bottom pb-2">Información General</h5>
        <div class="row">
          <div class="col-md-6 mb-2"><strong>ID:</strong> ${proveedorEjemplo.supplierId || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Nombre:</strong> ${proveedorEjemplo.companyName || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Email:</strong> ${proveedorEjemplo.email || proveedorEjemplo.companyOwnerEmail || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>País:</strong> ${proveedorEjemplo.country || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Ciudad:</strong> ${proveedorEjemplo.city || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Dirección:</strong> ${proveedorEjemplo.address || 'No especificada'}</div>
          <div class="col-md-6 mb-2"><strong>Estado:</strong> <span class="badge bg-success">${proveedorEjemplo.status || 'No definido'}</span></div>
          <div class="col-md-6 mb-2"><strong>Fecha de creación:</strong> ${fechaCreacion}</div>
        </div>
      </div>

      <div class="mb-4">
        <h5 class="border-bottom pb-2">Información de Contacto</h5>
        <div class="row">
          <div class="col-md-6 mb-2"><strong>Nombre del Dueño:</strong> ${proveedorEjemplo.companyOwnerName || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Email del Dueño:</strong> ${proveedorEjemplo.companyOwnerEmail || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Teléfono del Dueño:</strong> ${proveedorEjemplo.companyOwnerPhone || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Nombre de Contacto:</strong> ${proveedorEjemplo.contactName || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Teléfono:</strong> ${proveedorEjemplo.phone || 'No especificado'}</div>
        </div>
      </div>

      <div class="mb-4">
        <h5 class="border-bottom pb-2">Información Bancaria</h5>
        <div class="row">
          <div class="col-md-6 mb-2"><strong>Términos de Pago:</strong> ${proveedorEjemplo.paymentsTerms || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Moneda:</strong> ${proveedorEjemplo.currency || 'No especificada'}</div>
          <div class="col-md-6 mb-2"><strong>Banco:</strong> ${proveedorEjemplo.bankName || 'No especificado'}</div>
          <div class="col-md-6 mb-2"><strong>Cuenta:</strong> ${proveedorEjemplo.accountNumber || 'No especificada'}</div>
        </div>
      </div>
    </div>
  `;

  // Mostrar el Swal con los detalles
  Swal.fire({
    title: proveedorEjemplo.companyName || 'Detalles del Proveedor',
    html: contenidoHTML,
    width: '800px',
    showCloseButton: true,
    showConfirmButton: false
  });
}

// Función para cambiar el estado de un proveedor
function cambiarEstadoProveedor(data: { proveedor: Proveedor; nuevoEstado: string; comentario: string }) {
  const { proveedor, nuevoEstado, comentario } = data;

  // Encontrar el proveedor en el array original y actualizar su estado
  const proveedorIndex = proveedores.value.findIndex(p => p.supplierId === proveedor.supplierId);

  if (proveedorIndex !== -1) {
    // Actualizar el estado
    proveedores.value[proveedorIndex].status = nuevoEstado;

    // En un entorno real, aquí haríamos una llamada a la API para actualizar el estado en el backend
    console.log(`Proveedor ${proveedor.companyName} actualizado a estado ${nuevoEstado}`);
    console.log(`Comentario: ${comentario}`);

    // Actualizar también en el array filtrado si existe
    const proveedorFiltradoIndex = proveedoresFiltrados.value.findIndex(p => p.supplierId === proveedor.supplierId);
    if (proveedorFiltradoIndex !== -1) {
      proveedoresFiltrados.value[proveedorFiltradoIndex].status = nuevoEstado;
    }

    // Mostrar mensaje de éxito
    Swal.fire({
      title: 'Estado Actualizado',
      text: `El estado del proveedor ha sido actualizado a "${nuevoEstado}".`,
      icon: 'success',
      confirmButtonText: 'Aceptar'
    });
  }
}
</script>

<style scoped>
.header-box {
  background-color: rgba(255, 255, 255, 0.197);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-radius: 12px;
}

.cotizaciones-view {
  background-color: #f5f5f5a8;
  overflow: auto;
}
</style>
