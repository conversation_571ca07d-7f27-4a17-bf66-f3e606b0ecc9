<template>
  <header
    class="header-box d-flex justify-content-start align-items-center mb-4 px-3 py-3 rounded animate-fade"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      stroke-width="1.5"
      stroke="currentColor"
      class="size-15"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
      />
    </svg>
    <h1 class="fw-bold text-secondary p-2 m-0">Cotizaciones</h1>
  </header>
  <main class="animate-fade animate-once animate-duration-[2000ms] animate-delay-500">
    <div class="cotizaciones-view p-4">
      <!-- Filtros -->
      <CotizacionFilter @search="aplicarFiltros" />

      <!-- Tabla de Cotizaciones -->
      <CotizacionTable
        :cotizaciones="cotizacionesFiltradas"
        @ver-cotizacion="abrirCotizacion"
        @actualizar="actualizarCotizacion"
        @eliminar="eliminarCotizacion"
      />

      <!-- Mensaje cuando no hay cotizaciones -->
      <div v-if="cotizacionesFiltradas.length === 0" class="no-data-message">
        <div class="alert alert-info">
          No se encontraron cotizaciones que coincidan con los filtros aplicados.
        </div>
      </div>

      <!-- Modal de Detalle de Cotización -->
      <DetalleCotizacionModal
        :visible="mostrarDetalleCotizacion"
        :cotizacion="cotizacionSeleccionada"
        @close="cerrarDetalleCotizacion"
        @update="guardarCambiosCotizacion"
        @cotizacion-modificada="marcarCotizacionModificada"
      />
    </div>
  </main>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import Swal from 'sweetalert2';
import CotizacionFilter from "@/components/filters/CotizacionFilter.vue";
import CotizacionTable from "@/components/tables/CotizacionTable.vue";
import DetalleCotizacionModal from "@/components/modals/DetalleCotizacionModal.vue";
import type { Cotizacion } from "@/interfaces/CotizacionInterface";
import { mockCotizacion } from "@/mocks/mockCotizaciones";
import keycloak from "@/auth/keycloak";
import { useAuth } from "@/composables/useAuth";
import { jwtDecode } from "jwt-decode";
import { useRouter } from "vue-router";

// validación de permiso de acceso a ruta según role
const { hasRole } = useAuth();
const router = useRouter();
const token = keycloak.token;
if (token) {
  const decoded: any = jwtDecode(token);
  console.log("🧾 Token Decodificado:", decoded);
  console.log("Has roles:", decoded.resource_access?.myclient?.roles);
}

const allowedRoles = ["Sourcing", "Auditor", "Diseño"];

onMounted(() => {
  const isAuthorized = allowedRoles.some((role) => hasRole(role));

  if (!isAuthorized) {
    router.push({ name: "Home" });
  }
});

// Crear más datos de ejemplo para tener varias cotizaciones
const cotizacionesEjemplo: Cotizacion[] = [
  mockCotizacion,
  {
    ...mockCotizacion,
    id: 'COT-002',
    nombre: 'Cotización Textiles 2024',
    fechaCreacion: new Date(2024, 0, 15).toISOString(),
    tipo: 'enviada' as const,
    estado: 'pendiente',
    modificada: true
  },
  {
    ...mockCotizacion,
    id: 'COT-003',
    nombre: 'Cotización Muebles',
    fechaCreacion: new Date(2024, 1, 20).toISOString(),
    tipo: 'recibida' as const,
    estado: 'aprobado',
    modificada: false
  }
];

// Estado de las cotizaciones
const cotizaciones = ref<Cotizacion[]>(cotizacionesEjemplo);
const cotizacionesFiltradas = ref<Cotizacion[]>([]);
const cotizacionSeleccionada = ref<Cotizacion | null>(null);
const cotizacionesModificadas = ref(new Set<string>());

// Estado del modal de detalle
const mostrarDetalleCotizacion = ref(false);

// Cargar cotizaciones al montar el componente
onMounted(() => {
  // En un entorno real, aquí se cargarían las cotizaciones desde una API
  cotizacionesFiltradas.value = [...cotizaciones.value];
});

// Función para aplicar filtros a las cotizaciones
function aplicarFiltros(filtros: any) {
  cotizacionesFiltradas.value = cotizaciones.value.filter((c) => {
    const coincideID = filtros.id
      ? c.id.toLowerCase().includes(filtros.id.toLowerCase())
      : true;
    const coincideProveedor = filtros.proveedor
      ? c.proveedores.some((p) =>
          p.tipo === "existente"
            ? p.companyName?.toLowerCase().includes(filtros.proveedor.toLowerCase())
            : p.email?.toLowerCase().includes(filtros.proveedor.toLowerCase())
        )
      : true;
    const coincideTipo = filtros.tipo ? c.tipo === filtros.tipo : true;

    const fechaCreacion = new Date(c.fechaCreacion);
    const desde = filtros.fechaDesde ? new Date(filtros.fechaDesde) : null;
    const hasta = filtros.fechaHasta ? new Date(filtros.fechaHasta) : null;

    const coincideFecha =
      (!desde || fechaCreacion >= desde) && (!hasta || fechaCreacion <= hasta);

    return coincideID && coincideProveedor && coincideTipo && coincideFecha;
  });
}

// Función para abrir el modal de detalle de cotización
function abrirCotizacion(cotizacion: Cotizacion) {
  // Buscar en la lista original por ID y asignar la versión actualizada
  const cotizacionActual = cotizaciones.value.find(
    (c) => c.id === cotizacion.id
  );

  if (cotizacionActual) {
    // Crear una copia profunda para evitar modificaciones directas
    cotizacionSeleccionada.value = JSON.parse(JSON.stringify(cotizacionActual));
    mostrarDetalleCotizacion.value = true;
  }
}

// Función para cerrar el modal de detalle
function cerrarDetalleCotizacion() {
  mostrarDetalleCotizacion.value = false;
  cotizacionSeleccionada.value = null;
}

// Función para guardar los cambios realizados en la cotización
function guardarCambiosCotizacion(data: { cotizacion: Cotizacion, descripcionCambios: string }) {
  const { cotizacion, descripcionCambios } = data;

  // Buscar la cotización original y actualizarla
  const index = cotizaciones.value.findIndex(c => c.id === cotizacion.id);

  if (index !== -1) {
    // Actualizar la cotización en el array original
    cotizaciones.value[index] = { ...cotizacion };

    // Actualizar también en el array filtrado si existe
    const indexFiltrado = cotizacionesFiltradas.value.findIndex(c => c.id === cotizacion.id);
    if (indexFiltrado !== -1) {
      cotizacionesFiltradas.value[indexFiltrado] = { ...cotizacion };
    }

    // Agregar a la lista de cotizaciones modificadas
    cotizacionesModificadas.value.add(cotizacion.id);

    // En un entorno real, aquí se enviaría la actualización a una API
    console.log('Cotización actualizada:', cotizacion);
    console.log('Descripción de los cambios:', descripcionCambios);
  }
}

// Función para marcar una cotización como modificada
function marcarCotizacionModificada(id: string) {
  const cotizacion = cotizaciones.value.find(c => c.id === id);

  if (cotizacion) {
    cotizacion.modificada = true;
  }
}

// Función para actualizar una cotización y notificar a los proveedores
function actualizarCotizacion(id: string) {
  const cotizacion = cotizaciones.value.find(c => c.id === id);

  if (cotizacion) {
    Swal.fire({
      title: 'Actualizar Cotización',
      text: 'Ingrese una descripción de los cambios realizados para notificar a los proveedores:',
      input: 'textarea',
      inputPlaceholder: 'Descripción de los cambios...',
      showCancelButton: true,
      confirmButtonText: 'Enviar Notificación',
      cancelButtonText: 'Cancelar',
      inputValidator: (value) => {
        if (!value) {
          return 'Debe ingresar una descripción de los cambios';
        }
        return null;
      }
    }).then((result) => {
      if (result.isConfirmed) {
        // En un entorno real, aquí se enviaría la notificación a los proveedores
        console.log('Enviando notificación a los proveedores de la cotización:', id);
        console.log('Descripción de los cambios:', result.value);

        // Actualizar el estado de la cotización
        cotizacion.modificada = false;

        // Eliminar de la lista de cotizaciones modificadas
        cotizacionesModificadas.value.delete(id);

        Swal.fire(
          'Notificación Enviada',
          `Los proveedores han sido notificados de los cambios en la cotización ${cotizacion.id}.`,
          'success'
        );
      }
    });
  }
}

// Función para eliminar una cotización
function eliminarCotizacion(id: string) {
  const cotizacion = cotizaciones.value.find(c => c.id === id);

  if (cotizacion) {
    Swal.fire({
      title: 'Eliminar Cotización',
      text: 'Ingrese el motivo de la eliminación para informar a los proveedores:',
      input: 'textarea',
      inputPlaceholder: 'Motivo de la eliminación...',
      showCancelButton: true,
      confirmButtonText: 'Eliminar',
      cancelButtonText: 'Cancelar',
      confirmButtonColor: '#d33',
      inputValidator: (value) => {
        if (!value) {
          return 'Debe ingresar un motivo para la eliminación';
        }
        return null;
      }
    }).then((result) => {
      if (result.isConfirmed) {
        // En un entorno real, aquí se enviaría la notificación a los proveedores
        console.log('Enviando notificación de eliminación a los proveedores de la cotización:', id);
        console.log('Motivo de la eliminación:', result.value);

        // Eliminar la cotización de los arrays
        cotizaciones.value = cotizaciones.value.filter(c => c.id !== id);
        cotizacionesFiltradas.value = cotizacionesFiltradas.value.filter(c => c.id !== id);
        cotizacionesModificadas.value.delete(id);

        Swal.fire(
          'Cotización Eliminada',
          `La cotización ${id} ha sido eliminada y los proveedores han sido notificados.`,
          'success'
        );
      }
    });
  }
}
</script>

<style scoped>
.header-box {
  background-color: rgba(255, 255, 255, 0.197);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-radius: 12px;
}

.cotizaciones-view {
  background-color: #f5f5f5a8;
  overflow: auto;
  min-height: 70vh;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.no-data-message {
  padding: 2rem;
  text-align: center;
}

.alert {
  background-color: rgba(255, 255, 255, 0.8);
  border-left: 4px solid #17a2b8;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 0.25rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.alert-info {
  border-left-color: #17a2b8;
}
</style>
