<template>
  <form
    @submit.prevent="emitSearch"
    class="flex flex-wrap gap-4 items-end bg-gray-100 p-4 rounded shadow"
  >
    <div class="flex flex-col">
      <label for="id" class="text-sm font-semibold">ID Cotización</label>
      <input
        id="id"
        v-model="filters.id"
        type="text"
        class="form-input"
        placeholder="Ej: COT1234"
      />
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-semibold">Fecha Desde</label>
      <input type="date" v-model="filters.fechaDesde" class="form-input" />
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-semibold">Fecha <PERSON></label>
      <input type="date" v-model="filters.fechaHasta" class="form-input" />
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-semibold">Proveedor</label>
      <input
        v-model="filters.proveedor"
        type="text"
        class="form-input"
        placeholder="Nombre o Tax ID"
      />
    </div>

    <div class="flex flex-col col-sm">
      <label class="text-sm font-semibold">Status</label>
      <select v-model="filters.tipo" class="form-input">
        <option value="">Todos</option>
        <option value="enviada">Enviadas</option>
        <option value="recibida">Recibidas</option>
      </select>
    </div>

    <div class="flex flex-col col-sm">
      <button type="submit" class="btn-action px-2 py-2 mt-2">Buscar</button>
      <button
        type="button"
        class="btn-secondary px-6 py-2 mt-2"
        @click="limpiarFiltros"
      >
        Limpiar 
      </button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { reactive } from "vue";

const emit = defineEmits(["search"]);

const filters = reactive({
  id: "",
  fechaDesde: "",
  fechaHasta: "",
  proveedor: "",
  tipo: "",
});

function limpiarFiltros() {
  filters.id = "";
  filters.fechaDesde = "";
  filters.fechaHasta = "";
  filters.proveedor = "";
  filters.tipo = "";
}

function emitSearch() {

  emit("search", { ...filters });
}
</script>

<style scoped>
form{
  background-color: #ffffff32;
  margin-bottom: 1rem;
}

.form-input {
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  padding: 0.5rem 0.5rem;
  font-size: 0.875rem;
}

.btn-action {
  background-color: #2563eb;
  color: white;
  font-weight: 600;
  border-radius: 0.25rem;
  padding: 0.5rem 1.5rem;
  margin-top: 0.5rem;
}

.btn-action:hover {
  background-color: #1d4ed8;
  transition: background-color 0.2s;
}

.btn-secondary {
  background-color: #6b7280; /* Tailwind: gray-500 */
  color: white;
  font-weight: 600;
  border-radius: 0.25rem;
  padding: 0.5rem 1.5rem;
  margin-top: 0.5rem;
}

.btn-secondary:hover {
  background-color: #4b5563; /* Tailwind: gray-600 */
  transition: background-color 0.2s;
}
</style>
