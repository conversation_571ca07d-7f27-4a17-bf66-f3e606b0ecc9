<template>
  <div class="envia-correo">
    <div class="container background-correo">
      <!-- Selector de plantillas -->
      <div class="template-selector">
        <label>Seleccionar plantilla:</label>
        <Dropdown
          v-model="selectedTemplate"
          :options="emailTemplates"
          optionLabel="name"
          placeholder="Seleccione una plantilla"
          class="w-full"
          @change="applyTemplate"
        />
      </div>

      <!-- Formulario estilo Gmail -->
      <div class="gmail-form">
        <!-- De -->
        <div class="form-row">
          <div class="form-label">De:</div>
          <div class="form-input">
            <InputText
              v-model="emailData.from"
              :value="userEmail"
              class="w-full"
              @blur="validateEmailSent('from')" />
            <small v-if="emailData.errors.from" class="error-message">{{ emailData.errors.from }}</small>
          </div>
        </div>

        <!-- Para -->
        <div class="form-row">
          <div class="form-label">Para:</div>
          <div class="form-input">
            <InputText
              v-model="emailData.to"
              :value="props.email"
              placeholder="<EMAIL>"
              class="w-full"
              @blur="validateEmailSent('to')" />
            <small v-if="emailData.errors.to" class="error-message">{{ emailData.errors.to }}</small>
          </div>
        </div>

        <!-- CC -->
        <div class="form-row">
          <div class="form-label">CC:</div>
          <div class="form-input">
            <InputText
              v-model="emailData.cc"
              placeholder="<EMAIL>"
              class="w-full"
              @blur="validateEmailSent('cc')" />
            <small v-if="emailData.errors.cc" class="error-message">{{ emailData.errors.cc }}</small>
          </div>
        </div>

        <!-- Asunto -->
        <div class="form-row">
          <div class="form-label">Asunto:</div>
          <div class="form-input">
            <InputText
              v-model="emailData.subject"
              placeholder="Asunto del correo"
              class="w-full"
              @blur="validateEmailSent('subject')" />
            <small v-if="emailData.errors.subject" class="error-message">{{ emailData.errors.subject }}</small>
          </div>
        </div>

        <!-- Línea divisoria -->
        <div class="divider"></div>

        <!-- Cuerpo del correo -->
        <div class="form-body">
          <Textarea
            v-model="emailData.body"
            rows="12"
            class="w-full email-body"
            @blur="validateEmailSent('body')" />
          <small v-if="emailData.errors.body" class="error-message">{{ emailData.errors.body }}</small>
        </div>

        <!-- Adjuntos -->
        <div class="attachments-section">
          <div class="attachments-header">
            <i class="pi pi-paperclip"></i>
            <span>Adjuntos</span>
          </div>

          <FileUpload
            mode="advanced"
            :multiple="true"
            accept="image/*,application/pdf,.xls,.xlsx, application/vnd.ms-excel,.dock,.docx,application/msword,text/plain"
            :maxFileSize="50000000"
            @upload="onUpload"
            @select="onFileSelect"
            @remove="onFileRemove"
            :auto="true"
            chooseLabel="Adjuntar archivo"
            :customUpload="true">
            <template #empty>
              <p>Arrastra y suelta archivos aquí para adjuntarlos.</p>
            </template>
            <template #header="{ chooseCallback }">
              <Button
                severity="warn"
                variant="outlined"
                label="Importar"
                raised
                @click="chooseCallback()"
                class="bi bi-upload"
              />
            </template>
          </FileUpload>

          <div
            v-if="emailData.attachments.length > 0"
            class="attachments-list">
            <div
              v-for="(attachment, index) in emailData.attachments"
              :key="index"
              class="attachment-item">
              <i class="pi pi-file"></i>
              <span class="attachment-name">{{ attachment.name }}</span>
              <span class="attachment-size">({{ formatFileSize(attachment.size) }})</span>
              <Button
              severity="danger"
              variant="text"
              class="bi bi-x-lg btn-dell-adjun"
              @click="removeAttachment(index)" />
            </div>
          </div>
        </div>

        <!-- Botones de acción -->
        <div class="action-buttons">
          <Button
            label="Enviar"
            icon="pi pi-send"
            class="p-button-primary"
            @click="sendEmail" />
          <Button
            label="Descartar"
            icon="pi pi-trash"
            class="p-button-secondary"
            @click="resetForm" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { InputText, Textarea } from "primevue";
import FileUpload from "primevue/fileupload";
import Button from "primevue/button";
import Dropdown from "primevue/dropdown";
import { useAuth } from "@/composables/useAuth";
import Swal from "sweetalert2";

// Obtener el email del usuario registrado
const { getEmail } = useAuth();
const userEmail = getEmail();
const props = defineProps({
  email: String,
});

const emit = defineEmits(['email-sent']);

// Interfaces
interface Attachment {
  name: string;
  size: number;
  type: string;
  content: File;
}

interface EmailData {
  from: string;
  to: string;
  cc: string;
  subject: string;
  body: string;
  attachments: Attachment[];
  errors: {
    from: string;
    to: string;
    cc: string;
    subject: string;
    body: string;
  };
}

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
}

// Estado del formulario
const emailData = ref<EmailData>({
  from: "",
  to: "",
  cc: "",
  subject: "",
  body: "",
  attachments: [],
  errors: {
    from: "",
    to: "",
    cc: "",
    subject: "",
    body: "",
  },
});

// Plantillas de correo
const emailTemplates = ref<EmailTemplate[]>([
  {
    id: "new_quotation",
    name: "Enviar cotización",
    subject: "Nueva cotización para su revisión",
    body: `Estimado proveedor,

Nos complace enviarle una nueva cotización para su revisión. Adjunto encontrará los detalles de los productos solicitados.

Por favor, revise la información y envíenos su mejor oferta antes de la fecha límite indicada.

Si tiene alguna pregunta o necesita aclaraciones adicionales, no dude en contactarnos.

Saludos cordiales,
[Su nombre]
[Su empresa]`
  },
  {
    id: "modified_quotation",
    name: "Cotización modificada",
    subject: "Actualización de cotización",
    body: `Estimado proveedor,

Le informamos que hemos realizado modificaciones a la cotización previamente enviada. Los cambios incluyen:

[Detallar los cambios realizados]

Por favor, revise estos cambios y actualice su oferta en consecuencia.

Agradecemos su atención y quedamos a la espera de su respuesta.

Saludos cordiales,
[Su nombre]
[Su empresa]`
  },
  {
    id: "deleted_quotation",
    name: "Cotización borrada",
    subject: "Cancelación de cotización",
    body: `Estimado proveedor,

Le informamos que la cotización [ID de cotización] ha sido cancelada por el siguiente motivo:

[Motivo de cancelación]

Agradecemos su comprensión y esperamos poder trabajar con ustedes en futuras oportunidades.

Saludos cordiales,
[Su nombre]
[Su empresa]`
  },
  {
    id: "accepted_quotation",
    name: "Cotización aceptada",
    subject: "Confirmación de aceptación de cotización",
    body: `Estimado proveedor,

Nos complace informarle que su cotización [ID de cotización] ha sido aceptada.

Próximamente nos pondremos en contacto para coordinar los detalles de entrega y pago.

Agradecemos su participación y esperamos mantener una relación comercial exitosa.

Saludos cordiales,
[Su nombre]
[Su empresa]`
  },
  {
    id: "new_supplier",
    name: "Proveedor nuevo",
    subject: "Invitación a registro de proveedor",
    body: `Estimado proveedor,

Nos gustaría invitarle a formar parte de nuestra red de proveedores. Hemos identificado su empresa como un potencial socio comercial y estamos interesados en establecer una relación de negocios.

Para iniciar el proceso de registro, por favor complete el formulario adjunto con la información de su empresa.

Si tiene alguna pregunta, no dude en contactarnos.

Saludos cordiales,
[Su nombre]
[Su empresa]`
  },
  {
    id: "supplier_update",
    name: "Actualización de proveedor",
    subject: "Solicitud de actualización de información",
    body: `Estimado proveedor,

Le escribimos para solicitar una actualización de la información de su empresa en nuestro sistema.

Por favor, revise y actualice los datos en el siguiente enlace: [Enlace al formulario]

Es importante mantener su información actualizada para asegurar una comunicación eficiente y procesos de compra sin contratiempos.

Saludos cordiales,
[Su nombre]
[Su empresa]`
  },
  {
    id: "supplier_registered",
    name: "Proveedor registrado",
    subject: "Confirmación de registro exitoso",
    body: `Estimado proveedor,

Nos complace informarle que su empresa ha sido registrada exitosamente en nuestro sistema de proveedores.

A partir de ahora, podrá recibir solicitudes de cotización y participar en nuestros procesos de compra.

Le damos la bienvenida y esperamos construir una relación comercial duradera y beneficiosa para ambas partes.

Saludos cordiales,
[Su nombre]
[Su empresa]`
  }
]);

// Plantilla seleccionada
const selectedTemplate = ref<EmailTemplate | null>(null);

// Aplicar plantilla seleccionada
const applyTemplate = () => {
  if (selectedTemplate.value) {
    emailData.value.subject = selectedTemplate.value.subject;
    emailData.value.body = selectedTemplate.value.body;
  }
};

// Validación de campos
const validateEmailSent = (fieldName: keyof EmailData) => {
  const data = emailData.value;
  data.errors[fieldName as keyof typeof data.errors] = "";

  switch (fieldName) {
    case "from":
      if (!data.from) {
        data.errors.from = "El correo del remitente es obligatorio";
      } else if (!validateEmail(data.from)) {
        data.errors.from = "Formato de correo inválido";
      }
      break;

    case "to":
      if (!data.to) {
        data.errors.to = "El correo del destinatario es obligatorio";
      } else if (!validateEmail(data.to)) {
        data.errors.to = "Formato de correo inválido";
      }
      break;

    case "cc":
      if (data.cc && !validateEmail(data.cc)) {
        data.errors.cc = "Formato de correo inválido";
      }
      break;

    case "subject":
      if (!data.subject) {
        data.errors.subject = "El asunto es obligatorio";
      }
      break;

    case "body":
      if (!data.body) {
        data.errors.body = "Es necesario escribir un mensaje en el cuerpo del correo";
      }
      break;
  }
  return !data.errors[fieldName as keyof typeof data.errors];
};

// Validar formato de correo electrónico
const validateEmail = (email: string): boolean => {
  if (!email) return true; // Si está vacío, no validamos (la obligatoriedad se valida en otra parte)
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Manejo de archivos adjuntos
const onFileSelect = (event: any) => {
  const files = event.files;

  for (let file of files) {
    const attachment: Attachment = {
      name: file.name,
      size: file.size,
      type: file.type,
      content: file,
    };

    emailData.value.attachments.push(attachment);
  }
};

const onFileRemove = (event: any) => {
  const removedFile = event.file;

  emailData.value.attachments = emailData.value.attachments.filter(
    (attachment) => attachment.name !== removedFile.name
  );
};

const onUpload = () => {
  // Este método es necesario pero la carga real la manejamos en onFileSelect
  // porque estamos usando customUpload=true
};

const removeAttachment = (index: number) => {
  emailData.value.attachments.splice(index, 1);
};

// Formatear tamaño de archivo
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// Restablecer formulario
const resetForm = () => {
  emailData.value = {
    from: "",
    to: "",
    cc: "",
    subject: "",
    body: "",
    attachments: [],
    errors: {
      from: "",
      to: "",
      cc: "",
      subject: "",
      body: "",
    },
  };
  selectedTemplate.value = null;
};

// Enviar correo
const sendEmail = async () => {
  // Validar todos los campos antes de enviar
  const isFromValid = validateEmailSent('from');
  const isToValid = validateEmailSent('to');
  const isCcValid = validateEmailSent('cc');
  const isSubjectValid = validateEmailSent('subject');
  const isBodyValid = validateEmailSent('body');

  if (!isFromValid || !isToValid || !isCcValid || !isSubjectValid || !isBodyValid) {
    console.error("Hay errores en el formulario. Por favor, corríjalos antes de enviar.");
    return;
  }

  try {
    // Aquí crearíamos un FormData para enviar el email con los adjuntos
    const formData = new FormData();

    formData.append("to", emailData.value.to);
    formData.append("subject", emailData.value.subject);
    formData.append("from", emailData.value.from);
    formData.append("cc", emailData.value.cc);
    formData.append("body", emailData.value.body);

    // Agregar cada archivo adjunto al FormData
    emailData.value.attachments.forEach((attachment, index) => {
      formData.append(
        `attachment_${index}`,
        attachment.content,
        attachment.name
      );
    });

    // Ejemplo de envío al servidor (necesitarás implementar tu endpoint)
    // const response = await axios.post('/api/send-email', formData);

    console.log("Email preparado para enviar:", emailData.value);
    console.log("FormData creado con adjuntos");

    // Simulación de envío exitoso
    Swal.fire({
      title: "¡Éxito!",
      text: "Correo enviado con éxito",
      icon: "success",
      timer: 1500,
      showConfirmButton: false
    });

    // Emitir evento de correo enviado
    emit('email-sent');

    // Restablecer el formulario después del envío
    resetForm();
  } catch (error) {
    console.error("Error al enviar el email:", error);
    alert("Error al enviar el correo. Por favor, inténtelo de nuevo.");
  }
};
</script>
<style scoped>
/* Estilos generales */
.envia-correo {
  font-family: 'Roboto', Arial, sans-serif;
  color: #202124;
}

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

.background-correo {
  background-color: #ffffff;
  box-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
  border-radius: 8px;
  padding: 20px;
}

/* Selector de plantillas */
.template-selector {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.template-selector label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #5f6368;
}

/* Formulario estilo Gmail */
.gmail-form {
  display: flex;
  flex-direction: column;
}

.form-row {
  display: flex;
  margin-bottom: 10px;
  align-items: flex-start;
  min-height: 40px;
}

.form-label {
  width: 60px;
  padding-top: 8px;
  color: #5f6368;
  font-weight: 500;
  flex-shrink: 0;
}

.form-input {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 15px 0;
}

.form-body {
  margin-bottom: 20px;
}

.email-body {
  border: none;
  padding: 10px;
  background-color: #ffffff;
  resize: vertical;
  min-height: 200px;
  font-family: 'Roboto', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
}

.email-body:focus {
  outline: none;
}

/* Estilos para los inputs */
:deep(.p-inputtext) {
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #e0e0e0;
  padding: 8px 0;
  transition: border-color 0.2s;
}

:deep(.p-inputtext:focus) {
  outline: none;
  border-color: #1a73e8;
  box-shadow: none;
}

:deep(.p-dropdown) {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

:deep(.p-dropdown:focus) {
  border-color: #1a73e8;
  box-shadow: 0 0 0 1px #1a73e8;
}

/* Mensajes de error */
.error-message {
  color: #d93025;
  font-size: 12px;
  margin-top: 4px;
}

/* Sección de adjuntos */
.attachments-section {
  margin-bottom: 20px;
}

.attachments-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #5f6368;
}

.attachments-header i {
  margin-right: 8px;
}

:deep(.p-fileupload) {
  border: 1px dashed #dadce0;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
}

:deep(.p-fileupload-buttonbar) {
  background: transparent;
  border: none;
  padding: 0;
}

:deep(.p-fileupload-content) {
  background: transparent;
  border: none;
  padding: 10px 0 0 0;
}

.attachments-list {
  margin-top: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #e0e0e0;
}

.attachment-item:last-child {
  border-bottom: none;
}

.attachment-item i {
  color: #5f6368;
  margin-right: 8px;
}

.attachment-name {
  flex-grow: 1;
  margin-right: 8px;
}

.attachment-size {
  color: #5f6368;
  font-size: 12px;
  margin-right: 8px;
}

/* Botones de acción */
.action-buttons {
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  margin-top: 20px;
}

:deep(.p-button) {
  border-radius: 4px;
  font-weight: 500;
  text-transform: none;
  transition: background-color 0.2s;
}

:deep(.p-button-primary) {
  background-color: #1a73e8;
  border-color: #1a73e8;
}

:deep(.p-button-primary:hover) {
  background-color: #1765cc;
  border-color: #1765cc;
}

:deep(.p-button-secondary) {
  background-color: #f1f3f4;
  border-color: #f1f3f4;
  color: #5f6368;
}

:deep(.p-button-secondary:hover) {
  background-color: #e8eaed;
  border-color: #e8eaed;
}

/* Responsive */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }

  .form-label {
    width: 100%;
    padding-bottom: 4px;
    padding-top: 0;
  }

  .action-buttons {
    flex-direction: column;
  }

  :deep(.p-button) {
    width: 100%;
  }
}
</style>
