<template>
  <div class="container-fluid min-vh-100">
    <UserHeader title="Cotizaciones" icon="card-list" />

    <main class="quotation-page d-flex justify-center">
      <div class="d-flex flex-column justify-around gap-3">
        <GradientButton @click="goTo('/cotizaciones/nueva')" class="animate-fade-up animate-once animate-duration-500 animate-delay-[200ms]">
          Nueva Cotización
        </GradientButton>

        <GradientButton @click="goTo('/cotizaciones/view')" class="animate-fade-up animate-once animate-duration-500 animate-delay-[400ms]">
          Cotizaciones
        </GradientButton>

        <GradientButton @click="goTo('/cotizaciones/comparador')" class="animate-fade-up animate-once animate-duration-500 animate-delay-[600ms]">
          Comparador de Proveedores
        </GradientButton>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import UserHeader from '@/components/layout/headers/header.vue'
import GradientButton from '@/components/ui/GradientButton.vue'
import { useRouter } from 'vue-router'
import keycloak from "@/auth/keycloak";
import { useAuth } from "@/composables/useAuth";
import { jwtDecode } from "jwt-decode";
import { onMounted } from 'vue';
// validación de permiso de acceso a ruta según role
const { hasRole } = useAuth();
const token = keycloak.token;
if (token) {
  const decoded: any = jwtDecode(token);
  console.log("🧾 Token Decodificado:", decoded);
  console.log("Has roles:", decoded.resource_access?.myclient?.roles);
}

const allowedRoles = ["Sourcing", "Auditor", "Diseño"];

onMounted(() => {
  const isAuthorized = allowedRoles.some((role) => hasRole(role));

  if (!isAuthorized) {
    router.push({ name: "Home" });
  }
});

const router = useRouter()
const goTo = (path: string) => router.push(path)
</script>

<style scoped>
.container-fluid {
  padding: 0;
}

.quotation-page {
  min-height: 70vh;
  padding: 1rem;
}

@media (max-width: 412px) {
  .quotation-page button {
    font-size: 1rem;
    min-width: 260px;
  }
}
</style>
