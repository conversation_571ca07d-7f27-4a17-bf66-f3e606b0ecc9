<template>
  <div v-if="visible" class="modal-overlay">
    <div class="modal-container">
      <!-- Encabezado del modal -->
      <div class="modal-header">
        <h2 class="modal-title">{{ proveedor?.companyName || 'Detalles del Proveedor' }}</h2>
        <button class="btn-close" @click="$emit('close')">×</button>
      </div>

      <!-- Contenido del modal -->
      <div class="modal-content">
        <!-- Pestañas de navegación -->
        <div class="tabs">
          <button
            v-for="(tab, index) in tabs"
            :key="index"
            :class="['tab-button', { active: activeTab === index }]"
            @click="activeTab = index"
          >
            <i :class="tab.icon"></i> {{ tab.name }}
          </button>
        </div>

        <!-- Contenido de las pestañas -->
        <div class="tab-content">
          <!-- Información General -->
          <div v-if="activeTab === 0" class="tab-pane">
            <h3>Información General</h3>
            <div class="form-grid">
              <div class="form-group">
                <label>ID</label>
                <input v-model="editedProveedor.supplierId" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Nombre de Empresa</label>
                <input v-model="editedProveedor.companyName" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Email</label>
                <input v-model="editedProveedor.email" type="email" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Estado</label>
                <div v-if="!editing" class="status-badge" :class="getStatusClass(editedProveedor.status)">
                  {{ editedProveedor.status || 'No definido' }}
                </div>
                <select v-else v-model="editedProveedor.status" class="form-control">
                  <option value="Creado">Creado</option>
                  <option value="ActProveedor">Actualizado por Proveedor</option>
                  <option value="ActSourcing">Actualizado por Sourcing</option>
                  <option value="Activo">Activo</option>
                </select>
              </div>
              <div class="form-group">
                <label>País</label>
                <input v-model="editedProveedor.country" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Provincia</label>
                <input v-model="editedProveedor.province" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Ciudad</label>
                <input v-model="editedProveedor.city" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Dirección</label>
                <input v-model="editedProveedor.address" type="text" class="form-control" :disabled="!editing">
              </div>
            </div>
          </div>

          <!-- Información de Contacto -->
          <div v-if="activeTab === 1" class="tab-pane">
            <h3>Información de Contacto</h3>
            <div class="form-grid">
              <div class="form-group">
                <label>Nombre del Dueño</label>
                <input v-model="editedProveedor.companyOwnerName" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Email del Dueño</label>
                <input v-model="editedProveedor.companyOwnerEmail" type="email" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Teléfono del Dueño</label>
                <input v-model="editedProveedor.companyOwnerPhone" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Nombre de Contacto</label>
                <input v-model="editedProveedor.contactName" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Teléfono</label>
                <input v-model="editedProveedor.phone" type="text" class="form-control" :disabled="!editing">
              </div>
            </div>
          </div>

          <!-- Información Bancaria -->
          <div v-if="activeTab === 2" class="tab-pane">
            <h3>Información Bancaria</h3>
            <div class="form-grid">
              <div class="form-group">
                <label>Términos de Pago</label>
                <input v-model="editedProveedor.paymentsTerms" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Moneda</label>
                <input v-model="editedProveedor.currency" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Banco</label>
                <input v-model="editedProveedor.bankName" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Dirección del Banco</label>
                <input v-model="editedProveedor.bankAddress" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Código SWIFT</label>
                <input v-model="editedProveedor.swiftCode" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Número IBAN</label>
                <input v-model="editedProveedor.ibanNumber" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Número de Cuenta</label>
                <input v-model="editedProveedor.accountNumber" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Beneficiario</label>
                <input v-model="editedProveedor.beneficiaryName" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Dirección del Beneficiario</label>
                <input v-model="editedProveedor.beneficiaryAddress" type="text" class="form-control" :disabled="!editing">
              </div>
            </div>
          </div>

          <!-- Información Legal -->
          <div v-if="activeTab === 3" class="tab-pane">
            <h3>Información Legal</h3>
            <div class="form-grid">
              <div class="form-group">
                <label>Código de Crédito Social</label>
                <input v-model="editedProveedor.socialCreditCode" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Código de Licencia de Exportación</label>
                <input v-model="editedProveedor.exportLicenseCode" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Estado del Perfil de la Empresa</label>
                <input v-model="editedProveedor.companyProfileStatus" type="text" class="form-control" :disabled="!editing">
              </div>
              <div class="form-group">
                <label>Fecha de Creación</label>
                <input
                  :value="formatDate(editedProveedor.createdAt)"
                  type="text"
                  class="form-control"
                  disabled
                >
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pie del modal -->
      <div class="modal-footer">
        <div v-if="!editing">
          <button class="btn-primary" @click="startEditing">
            <i class="bi bi-pencil"></i> Editar
          </button>
          <button class="btn-secondary" @click="$emit('close')">
            <i class="bi bi-x"></i> Cerrar
          </button>
        </div>
        <div v-else>
          <button class="btn-success" @click="saveChanges">
            <i class="bi bi-check"></i> Guardar
          </button>
          <button class="btn-danger" @click="cancelEditing">
            <i class="bi bi-x"></i> Cancelar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { Proveedor } from '@/interfaces/ProveedorInterface';
import { mockProveedores } from '@/mocks/mockProveedores';
import Swal from 'sweetalert2';

const props = defineProps<{
  visible: boolean;
  proveedor: Proveedor | null;
}>();

const emit = defineEmits(['close', 'update', 'cambiarEstado']);

// Estado para la edición
const editing = ref(false);
const activeTab = ref(0);
const editedProveedor = ref<Proveedor>({
  items: [],
  correoPersonalizado: {
    subject: '',
    body: '',
    cc: []
  }
} as Proveedor);

// Pestañas de navegación
const tabs = [
  { name: 'General', icon: 'bi bi-info-circle' },
  { name: 'Contacto', icon: 'bi bi-person' },
  { name: 'Bancario', icon: 'bi bi-bank' },
  { name: 'Legal', icon: 'bi bi-file-text' }
];

// Inicializar el proveedor editado cuando cambia el proveedor seleccionado o la visibilidad
watch([() => props.proveedor, () => props.visible], ([newProveedor, newVisible]) => {
  console.log('Watch triggered - proveedor:', newProveedor, 'visible:', newVisible);

  if (newVisible) {
    try {
      // Si no hay proveedor o es nulo, usar el primer proveedor del mock
      const proveedorToUse = newProveedor || mockProveedores[0];

      // Crear una copia profunda para evitar modificar el original directamente
      const proveedorCopy = JSON.parse(JSON.stringify(proveedorToUse));

      // Asegurarse de que todos los campos necesarios existan
      if (!proveedorCopy.items) proveedorCopy.items = [];
      if (!proveedorCopy.correoPersonalizado) {
        proveedorCopy.correoPersonalizado = {
          subject: '',
          body: '',
          cc: []
        };
      }

      // Actualizar el proveedor editado
      editedProveedor.value = proveedorCopy;
      console.log('Proveedor cargado en el modal:', editedProveedor.value);

      // Forzar la actualización de la interfaz
      setTimeout(() => {
        console.log('Forzando actualización de la interfaz');
        // Esto fuerza a Vue a re-renderizar el componente
        activeTab.value = 0;
      }, 0);
    } catch (error) {
      console.error('Error al procesar el proveedor:', error);

      // En caso de error, usar directamente el primer proveedor del mock
      editedProveedor.value = { ...mockProveedores[0] };
      console.log('Usando proveedor de respaldo del mock');
    }
  }

  // Resetear el estado de edición
  editing.value = false;
  // Resetear la pestaña activa
  activeTab.value = 0;
}, { immediate: true, deep: true });

// Función para formatear fechas
function formatDate(dateString?: string): string {
  if (!dateString) return 'No especificada';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  } catch (error) {
    return dateString;
  }
}

// Función para obtener la clase CSS según el estado
function getStatusClass(status: string | undefined): string {
  if (!status) return 'status-default';

  console.log('Estado recibido:', status);

  switch(status) {
    case 'Activo':
      return 'status-active';
    case 'Creado':
      return 'status-created';
    case 'ActProveedor':
      return 'status-provider';
    case 'ActSourcing':
      return 'status-sourcing';
    default:
      return 'status-default';
  }
}

// Iniciar modo de edición
function startEditing() {
  editing.value = true;
}

// Cancelar edición
function cancelEditing() {
  // Restaurar los valores originales
  const originalProveedor = props.proveedor || mockProveedores[0];
  editedProveedor.value = JSON.parse(JSON.stringify(originalProveedor));
  editing.value = false;
}

// Guardar cambios
function saveChanges() {
  // Obtener el proveedor original (del prop o del mock si es necesario)
  const originalProveedor = props.proveedor || mockProveedores[0];

  // Verificar si hubo cambios
  const hasChanges = JSON.stringify(editedProveedor.value) !== JSON.stringify(originalProveedor);

  if (hasChanges) {
    // Mostrar modal para confirmar los cambios
    Swal.fire({
      title: 'Guardar Cambios',
      text: 'Indica los cambios realizados para notificar al proveedor:',
      input: 'textarea',
      inputPlaceholder: 'Descripción de los cambios...',
      showCancelButton: true,
      confirmButtonText: 'Guardar',
      cancelButtonText: 'Cancelar',
      inputValidator: (value) => {
        if (!value) {
          return 'Debes proporcionar una descripción de los cambios';
        }
        return null;
      }
    }).then((result) => {
      if (result.isConfirmed) {
        // Emitir evento para actualizar el proveedor
        emit('update', {
          proveedor: editedProveedor.value,
          comentario: result.value
        });

        // Si el estado cambió, emitir evento específico para cambio de estado
        if (originalProveedor?.status !== editedProveedor.value.status) {
          emit('cambiarEstado', {
            proveedor: editedProveedor.value,
            nuevoEstado: editedProveedor.value.status,
            comentario: result.value
          });
        }

        // Salir del modo edición
        editing.value = false;

        // Mostrar mensaje de éxito
        Swal.fire({
          title: 'Cambios Guardados',
          text: 'Los cambios han sido guardados y se ha notificado al proveedor.',
          icon: 'success',
          confirmButtonText: 'Aceptar'
        });
      }
    });
  } else {
    // No hubo cambios, simplemente salir del modo edición
    editing.value = false;
    Swal.fire({
      title: 'Sin Cambios',
      text: 'No se detectaron cambios en la información del proveedor.',
      icon: 'info',
      confirmButtonText: 'Aceptar'
    });
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.modal-title {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
}

.btn-close:hover {
  color: #343a40;
}

.modal-content {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Pestañas */
.tabs {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 1.5rem;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.2s;
}

.tab-button:hover {
  color: #495057;
  background-color: #f8f9fa;
}

.tab-button.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.tab-button i {
  margin-right: 0.5rem;
}

.tab-content {
  padding: 1rem 0;
}

.tab-pane {
  animation: fade-in 0.3s ease-out;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.tab-pane h3 {
  margin-bottom: 1.5rem;
  color: #495057;
  font-size: 1.2rem;
  border-bottom: 2px solid #f8f9fa;
  padding-bottom: 0.5rem;
}

/* Formulario */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

.form-control {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.2s;
}

.form-control:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control:disabled {
  background-color: #f8f9fa;
  opacity: 1;
}

/* Badges de estado */
.status-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-weight: 500;
  text-align: center;
  width: 100%;
}

.status-active {
  background-color: #28a745;
  color: white;
}

.status-created {
  background-color: #007bff;
  color: white;
}

.status-provider {
  background-color: #ffc107;
  color: #212529;
}

.status-sourcing {
  background-color: #17a2b8;
  color: white;
}

.status-default {
  background-color: #6c757d;
  color: white;
}

/* Botones */
.btn-primary, .btn-secondary, .btn-success, .btn-danger {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.25rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: 0.5rem;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0069d9;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover {
  background-color: #218838;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

/* Responsive */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .tabs {
    flex-wrap: wrap;
  }

  .tab-button {
    flex: 1 0 50%;
    text-align: center;
    padding: 0.5rem;
  }
}
</style>
