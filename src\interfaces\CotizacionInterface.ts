import type { Proveedor } from '@/interfaces/ProveedorInterface';
import type { Item } from '@/interfaces/ItemsInterface';

export interface Cotizacion {
  id: string;
  nombre: string;
  fechaCreacion: string;
  tipo: "enviada" | "recibida";
  proveedores: Proveedor[];
  items: Item[]; // Ahora los items están a nivel de cotización
  estado?: string; // opcional: aprobado, rechazado, pendiente, etc.
  modificada?: boolean; // indica si la cotización ha sido modificada desde su creación || cambiar a fechaModificacion
}