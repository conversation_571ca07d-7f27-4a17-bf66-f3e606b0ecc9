import type { Madre } from "@/interfaces/SkuInterface";
export const mockMadres: Madre[] = [
    {
      nombre: "T-Shirts",
      skus: [
        {
          sku: "TSH-001",
          descripcion: "Camiseta algodón blanca",
          proveedores: [
            {
              tipo: "existente",
              taxId: "11111111-1",
              info: { nombre: "Textiles Sur", fabrica: "Fábrica A" }
            },
            {
              tipo: "existente",
              taxId: "22222222-2",
              info: { nombre: "Fábrica Norte", fabrica: "Fábrica B" }
            },
            {
              tipo: "existente",
              taxId: "88888888-8",
              info: { nombre: "Fábrica de Telas Primavera", fabrica: "Fábrica H" }
            }
          ]
        },
        {
          sku: "TSH-002",
          descripcion: "Camiseta poliéster negra",
          proveedores: [
            {
              tipo: "existente",
              taxId: "33333333-3",
              info: { nombre: "Ropa Chile", fabrica: "Fábrica C" }
            },
            {
              tipo: "existente",
              taxId: "44444444-4",
              info: { nombre: "Textiles Pepito", fabrica: "Fábrica D" }
            }
          ]
        },
        {
          sku: "TSH-003",
          descripcion: "Camiseta poliéster roja",
          proveedores: [
            {
              tipo: "existente",
              taxId: "44444444-4",
              info: { nombre: "Textiles Pepito", fabrica: "Fábrica D" }
            },
            {
              tipo: "existente",
              taxId: "22222222-2",
              info: { nombre: "Fábrica Norte", fabrica: "Fábrica B" }
            }
          ]
        },
        {
          sku: "TSH-004",
          descripcion: "Camiseta poliéster azul",
          proveedores: [
            {
              tipo: "existente",
              taxId: "55555555-5",
              info: { nombre: "Ropas Filomena", fabrica: "Fábrica E" }
            },
            {
              tipo: "existente",
              taxId: "33333333-3",
              info: { nombre: "Ropa Chile", fabrica: "Fábrica C" }
            }
          ]
        },
        {
          sku: "TSH-005",
          descripcion: "Camiseta poliéster verde",
          proveedores: [
            {
              tipo: "existente",
              taxId: "66666666-6",
              info: { nombre: "María Clothes", fabrica: "Fábrica F" }
            },
            {
              tipo: "existente",
              taxId: "11111111-1",
              info: { nombre: "Textiles Sur", fabrica: "Fábrica A" }
            }
          ]
        },
        {
          sku: "TSH-006",
          descripcion: "Camiseta poliéster amarilla",
          proveedores: [
            {
              tipo: "existente",
              taxId: "77777777-7",
              info: { nombre: "Hilados y Tejidos Aurora", fabrica: "Fábrica G" }
            },
            {
              tipo: "existente",
              taxId: "55555555-5",
              info: { nombre: "Ropas Filomena", fabrica: "Fábrica E" }
            }
          ]
        },
        {
          sku: "TSH-007",
          descripcion: "Camiseta poliéster naraja",
          proveedores: [
            {
              tipo: "existente",
              taxId: "88888888-8",
              info: { nombre: "Fábrica de Telas Primavera", fabrica: "Fábrica H" }
            },
            {
              tipo: "existente",
              taxId: "11111111-1",
              info: { nombre: "Textiles Sur", fabrica: "Fábrica A" }
            }
          ]
        },
        
        
      ]
    },
    {
      nombre: "Jeans",
      skus: [
        {
          sku: "JNS-101",
          descripcion: "Jeans azul clásico",
          proveedores: [
            {
              tipo: "existente",
              taxId: "44444444-4",
              info: { nombre: "Denim Corp", fabrica: "Fábrica D" }
            }
          ]
        }
      ]
    }
  ];
  