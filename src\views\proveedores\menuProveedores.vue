<template>
  <div class="container-fluid min-vh-100">
    <UserHeader title="Cotizaciones" icon="card-list" />

    <main class="quotation-page d-flex">
      <div class="d-flex flex-column gap-3 justify-content-evenly">
        <GradientButton
          @click="goTo('/nuevoProveedor/mail')"
          class="btn animate-fade-up animate-once animate-duration-500 animate-delay-[200ms]">
          Nuevo Proveedor
        </GradientButton>

        <GradientButton
          @click="goTo('/proveedores/listaProveedores')"
          class="btn animate-fade-up animate-once animate-duration-500 animate-delay-[400ms]">
          Lista de Proveedores
        </GradientButton>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import UserHeader from "@/components/layout/headers/header.vue";
import GradientButton from "@/components/ui/GradientButton.vue";
import { useRouter } from "vue-router";
import { onMounted } from "vue";
import keycloak from "@/auth/keycloak";
import { useAuth } from "@/composables/useAuth";
import { jwtDecode } from "jwt-decode";

const { hasRole } = useAuth();
const token = keycloak.token;
if (token) {
  const decoded: any = jwtDecode(token);
  console.log("🧾 Token Decodificado:", decoded);
  console.log("Has roles:", decoded.resource_access?.myclient?.roles);
}

const allowedRoles = ["Sourcing", "Auditor"];

onMounted(() => {
  const isAuthorized = allowedRoles.some((role) => hasRole(role));

  if (!isAuthorized) {
    router.push({ name: "Home" });
  }
});

const router = useRouter();
const goTo = (path: string) => router.push(path);
</script>

<style scoped>
.container-fluid {
  padding: 0;
}

.quotation-page {
  min-height: 70vh;
  padding: 1rem;
}

@media (max-width: 412px) {
  .quotation-page button {
    font-size: 1rem;
    min-width: 260px;
  }
}
</style>
