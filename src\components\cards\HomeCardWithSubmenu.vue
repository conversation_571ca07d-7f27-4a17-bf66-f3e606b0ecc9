<template>
  <div class="card-container">
    <div
      class="card p-4 align-items-center"
      :class="[`gradient-${type}`]"
      @click="openModal"
    >
      <i :class="`${icon} display-4 text-white`"></i>
      <h2 class="card-title text-white">{{ title }}</h2>
      <p class="card-text text-white">{{ subtitle }}</p>
      <div class="card-arrow">
        <i class="bi bi-chevron-right"></i>
      </div>
    </div>

    <!-- Modal de submenú -->
    <SubmenuModal
      :visible="isModalOpen"
      :title="title"
      :type="type"
      :menuItems="menuItems"
      @close="closeModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SubmenuModal from '@/components/modals/SubmenuModal.vue';

defineProps<{
  icon: string;
  title: string;
  subtitle: string;
  type: 'cotizacion' | 'proveedores' | 'auditoria' | 'reportes';
  menuItems: Array<{
    title: string;
    route: string;
    icon: string;
  }>;
}>();

const isModalOpen = ref(false);

function openModal() {
  isModalOpen.value = true;
}

function closeModal() {
  isModalOpen.value = false;
}
</script>

<style scoped>
.card-container {
  position: relative;
  transition: all 0.3s ease;
  width: 280px;
}

.card {
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  overflow: hidden;
  min-height: 250px;
}

.card:hover {
  transform: translateY(-10px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.2);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1rem;
}

.card-text {
  font-size: 1rem;
  opacity: 0.9;
}

.card-arrow {
  position: absolute;
  bottom: 10px;
  right: 15px;
  color: white;
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.card:hover .card-arrow {
  transform: translateX(5px);
}

/* Gradientes para los diferentes tipos de cards */
.gradient-cotizacion {
  background: linear-gradient(135deg, #4158D0, #C850C0);
}

.gradient-proveedores {
  background: linear-gradient(135deg, #0093E9, #80D0C7);
}

.gradient-auditoria {
  background: linear-gradient(135deg, #FF9A8B, #FF6A88);
}

.gradient-reportes {
  background: linear-gradient(135deg, #FFB347, #FFCC33);
}


</style>
